/**
 * Utility to check if environment variables are properly set
 */

/**
 * Check if Supabase environment variables are properly set
 */
export const checkSupabaseEnv = (): {
  isValid: boolean;
  url?: string;
  hasAnonKey: boolean;
  errors: string[];
} => {
  const errors: string[] = [];

  // Check NEXT_PUBLIC_SUPABASE_URL
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL;
  if (!supabaseUrl) {
    errors.push('NEXT_PUBLIC_SUPABASE_URL is not set');
  } else if (!supabaseUrl.startsWith('http')) {
    errors.push('NEXT_PUBLIC_SUPABASE_URL is invalid (should start with http)');
  }

  // Check NEXT_PUBLIC_SUPABASE_ANON_KEY
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY;
  if (!supabaseAnonKey) {
    errors.push('NEXT_PUBLIC_SUPABASE_ANON_KEY is not set');
  }

  return {
    isValid: errors.length === 0,
    url: supabaseUrl,
    hasAnonKey: !!supabaseAnonKey,
    errors
  };
};

/**
 * Log environment variables status to console
 */
export const logEnvStatus = (): void => {
  const supabaseEnv = checkSupabaseEnv();

  console.log('Environment variables status:');
  console.log('- Supabase URL:', supabaseEnv.url || 'Not set');
  console.log('- Supabase Anon Key:', supabaseEnv.hasAnonKey ? 'Set' : 'Not set');

  if (!supabaseEnv.isValid) {
    console.error('Environment variables errors:');
    supabaseEnv.errors.forEach(error => console.error(`- ${error}`));
    console.error('Please check your .env.local file and make sure all required environment variables are set.');
    console.error('');
    console.error('IMPORTANT: You need to create a .env.local file in the root of your project with the following content:');
    console.error('');
    console.error(getSampleEnvFile());
    console.error('');
    console.error('After creating the file, restart your development server with `npm run dev`');
    console.error('');
    console.error('If you already have a .env.local file, make sure it contains the correct Supabase URL and anon key.');
    console.error('You can find these values in your Supabase project dashboard under Settings > API.');
  }
};

/**
 * Get a sample .env.local file content
 */
export const getSampleEnvFile = (): string => {
  return `# Supabase configuration
# Replace these values with your actual Supabase project details
NEXT_PUBLIC_SUPABASE_URL=https://your-project-id.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key-here

# You can find these values in your Supabase project:
# 1. Go to https://app.supabase.io/
# 2. Click on your project
# 3. Go to Settings > API
# 4. Copy the URL and anon key from the "Project API keys" section
`;
};
