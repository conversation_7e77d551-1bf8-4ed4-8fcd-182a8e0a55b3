import { Chat, Message, User } from '@/types';

export const mockUsers: User[] = [
  {
    id: '1',
    name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    status: 'online',
  },
  {
    id: '2',
    name: '<PERSON><PERSON><PERSON>',
    email: 'rosh<PERSON><PERSON>@example.com',
    status: 'offline',
  },
  {
    id: '3',
    name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    status: 'offline',
  },
  {
    id: '4',
    name: '<PERSON><PERSON><PERSON>',
    email: '<EMAIL>',
    status: 'online',
  },
  {
    id: '5',
    name: 'South Euna',
    email: '<EMAIL>',
    status: 'offline',
  },
  {
    id: '6',
    name: 'Livonia',
    email: '<EMAIL>',
    status: 'offline',
  },
  {
    id: '7',
    name: 'Testing 12345',
    email: '<EMAIL>',
    status: 'offline',
  }
];

export const mockChats: Chat[] = [
  {
    id: '1',
    name: 'Test El Centro',
    type: 'group',
    participants: [mockUsers[0], mockUsers[1], mockUsers[2], mockUsers[3]],
    created_at: '2023-01-22T12:00:00Z',
    updated_at: '2023-01-22T12:00:00Z',
    is_demo: true,
  },
  {
    id: '2',
    name: 'Test Skope Final 5',
    type: 'direct',
    participants: [mockUsers[3], mockUsers[0]],
    created_at: '2023-01-20T12:00:00Z',
    updated_at: '2023-01-20T12:00:00Z',
    is_demo: true,
    support_message: 'This doesn\'t go on Tuesday...',
  },
  {
    id: '3',
    name: 'Periskope Team Chat',
    type: 'group',
    participants: [mockUsers[3], mockUsers[0], mockUsers[1]],
    created_at: '2023-01-19T12:00:00Z',
    updated_at: '2023-01-19T12:00:00Z',
    is_demo: true,
    is_internal: true,
  },
  {
    id: '4',
    name: '+91 99999 99999',
    type: 'direct',
    participants: [mockUsers[3], mockUsers[1]],
    created_at: '2023-01-18T12:00:00Z',
    updated_at: '2023-01-18T12:00:00Z',
    is_demo: true,
    is_signUp: true,
  },
  {
    id: '5',
    name: 'Test Demo17',
    type: 'direct',
    participants: [mockUsers[3], mockUsers[1]],
    created_at: '2023-01-17T12:00:00Z',
    updated_at: '2023-01-17T12:00:00Z',
    is_demo: true,
    is_content: true,
  },
  {
    id: '6',
    name: 'Test El Centro',
    type: 'direct',
    participants: [mockUsers[3], mockUsers[5]],
    created_at: '2023-01-16T12:00:00Z',
    updated_at: '2023-01-16T12:00:00Z',
    is_demo: true,
  },
  {
    id: '7',
    name: 'Testing group',
    type: 'group',
    participants: [mockUsers[3], mockUsers[6]],
    created_at: '2023-01-15T12:00:00Z',
    updated_at: '2023-01-15T12:00:00Z',
    is_demo: true,
  },
  {
    id: '8',
    name: 'Yasin 3',
    type: 'direct',
    participants: [mockUsers[3], mockUsers[0]],
    created_at: '2023-01-14T12:00:00Z',
    updated_at: '2023-01-14T12:00:00Z',
    is_demo: true,
  },
  {
    id: '9',
    name: 'Test Skope Final 9473',
    type: 'direct',
    participants: [mockUsers[3], mockUsers[2]],
    created_at: '2023-01-13T12:00:00Z',
    updated_at: '2023-01-13T12:00:00Z',
    is_demo: true,
  },
  {
    id: '10',
    name: 'Skope Demo',
    type: 'direct',
    participants: [mockUsers[3], mockUsers[0]],
    created_at: '2023-01-12T12:00:00Z',
    updated_at: '2023-01-12T12:00:00Z',
    is_demo: true,
  },
  {
    id: '11',
    name: 'Test Demo15',
    type: 'direct',
    participants: [mockUsers[3], mockUsers[1]],
    created_at: '2023-01-11T12:00:00Z',
    updated_at: '2023-01-11T12:00:00Z',
    is_demo: true,
  }
];

export const mockMessages: Message[] = [
  {
    id: '1',
    content: 'Hello, South Euna!',
    timestamp: '2023-01-22T12:01:00Z',
    sender_id: '1', // Roshang Artel
    chat_id: '1',
    read: true,
    created_at: '2023-01-22T12:01:00Z',
  },
  {
    id: '2',
    content: 'Hello',
    timestamp: '2023-01-22T22:07:00Z',
    sender_id: '4', // Periskope
    chat_id: '1',
    read: true,
    created_at: '2023-01-22T22:07:00Z',
  },
  {
    id: '3',
    content: 'test el centro',
    timestamp: '2023-01-23T10:49:00Z',
    sender_id: '4', // Periskope
    chat_id: '6',
    read: true,
    created_at: '2023-01-23T10:49:00Z',
  },
  {
    id: '4',
    content: 'testing',
    timestamp: '2023-01-23T09:57:00Z',
    sender_id: '4', // Periskope
    chat_id: '9',
    read: true,
    created_at: '2023-01-23T09:57:00Z',
  },
  {
    id: '5',
    content: 'Periskope Test message',
    timestamp: '2023-01-28T14:30:00Z',
    sender_id: '4', // Periskope
    chat_id: '3',
    read: true,
    created_at: '2023-01-28T14:30:00Z',
  },
  {
    id: '6',
    content: 'Hello, Livonia!',
    timestamp: '2023-01-24T08:21:00Z',
    sender_id: '6', // Livonia
    chat_id: '6',
    read: true,
    created_at: '2023-01-24T08:21:00Z',
  },
  {
    id: '7',
    content: 'CDERT',
    timestamp: '2023-01-25T09:48:00Z',
    sender_id: '1', // Roshang Artel
    chat_id: '8',
    read: true,
    created_at: '2023-01-25T09:48:00Z',
  }
];
