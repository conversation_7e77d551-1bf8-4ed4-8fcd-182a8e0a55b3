import { openDB, DBSchema, IDBPDatabase } from 'idb';
import { Chat, Message } from '@/types';

interface PeriskopeDB extends DBSchema {
  chats: {
    key: string;
    value: Chat;
    indexes: { 'by-update': string };
  };
  messages: {
    key: string;
    value: Message;
    indexes: { 'by-chat': string };
  };
}

const DB_NAME = 'periskope-chat-db';
const DB_VERSION = 1;

let dbPromise: Promise<IDBPDatabase<PeriskopeDB>>;

// Initialize the database
const initDB = async () => {
  if (!dbPromise) {
    dbPromise = openDB<PeriskopeDB>(DB_NAME, DB_VERSION, {
      upgrade(db) {
        // Create chats store
        const chatStore = db.createObjectStore('chats', {
          keyPath: 'id'
        });
        chatStore.createIndex('by-update', 'updated_at');

        // Create messages store
        const messageStore = db.createObjectStore('messages', {
          keyPath: 'id'
        });
        messageStore.createIndex('by-chat', 'chat_id');
      }
    });
  }
  return dbPromise;
};

// Chat operations
export const addChat = async (chat: Chat): Promise<void> => {
  // Validate chat object before storing
  if (!chat || !chat.id) {
    throw new Error('Cannot add chat to IndexedDB: Missing ID');
  }

  // Ensure all required fields are present
  if (!chat.name || !chat.type || !chat.participants || !chat.updated_at || !chat.created_at) {
    console.warn('Chat object is missing some required fields:', chat);

    // Add default values for missing fields to prevent errors
    chat.name = chat.name || 'Unnamed Chat';
    chat.type = chat.type || 'direct';
    chat.participants = chat.participants || [];
    chat.updated_at = chat.updated_at || new Date().toISOString();
    chat.created_at = chat.created_at || chat.updated_at;
  }

  try {
    const db = await initDB();
    await db.put('chats', chat);
    console.log(`Chat ${chat.id} successfully stored in IndexedDB`);
  } catch (error) {
    console.error('Error storing chat in IndexedDB:', error);
    console.error('Chat object that caused the error:', chat);
    throw error;
  }
};

export const getChat = async (id: string): Promise<Chat | undefined> => {
  const db = await initDB();
  return db.get('chats', id);
};

export const getAllChats = async (): Promise<Chat[]> => {
  const db = await initDB();
  return db.getAllFromIndex('chats', 'by-update');
};

export const deleteChat = async (id: string): Promise<void> => {
  const db = await initDB();
  await db.delete('chats', id);
};

// Message operations
export const addMessage = async (message: Message): Promise<void> => {
  // Validate message object before storing
  if (!message || !message.id) {
    throw new Error('Cannot add message to IndexedDB: Missing ID');
  }

  // Ensure all required fields are present
  if (!message.content || !message.chat_id || !message.sender_id || !message.timestamp) {
    console.warn('Message object is missing some required fields:', message);

    // Add default values for missing fields to prevent errors
    message.content = message.content || '';
    message.chat_id = message.chat_id || 'unknown-chat';
    message.sender_id = message.sender_id || 'unknown-sender';
    message.timestamp = message.timestamp || new Date().toISOString();
    message.created_at = message.created_at || message.timestamp;
    message.read = message.read !== undefined ? message.read : false;
  }

  try {
    const db = await initDB();
    await db.put('messages', message);
    console.log(`Message ${message.id} successfully stored in IndexedDB`);
  } catch (error) {
    console.error('Error storing message in IndexedDB:', error);
    console.error('Message object that caused the error:', message);
    throw error;
  }
};

export const getMessage = async (id: string): Promise<Message | undefined> => {
  const db = await initDB();
  return db.get('messages', id);
};

export const getMessagesByChat = async (chatId: string): Promise<Message[]> => {
  const db = await initDB();
  return db.getAllFromIndex('messages', 'by-chat', chatId);
};

export const deleteMessage = async (id: string): Promise<void> => {
  const db = await initDB();
  await db.delete('messages', id);
};

// Initialize the database with mock data
export const initializeWithMockData = async (chats: Chat[], messages: Message[]): Promise<void> => {
  try {
    console.log('Initializing IndexedDB with mock data');
    console.log(`Chats to add: ${chats.length}, Messages to add: ${messages.length}`);

    const db = await initDB();

    // Validate and filter chats
    const validChats = chats.filter(chat => {
      if (!chat || !chat.id) {
        console.warn('Skipping invalid chat (missing ID):', chat);
        return false;
      }
      return true;
    });

    // Add all valid chats
    const chatTx = db.transaction('chats', 'readwrite');
    await Promise.all([
      ...validChats.map(chat => {
        // Ensure required fields have values
        chat.name = chat.name || 'Unnamed Chat';
        chat.type = chat.type || 'direct';
        chat.participants = chat.participants || [];
        chat.updated_at = chat.updated_at || new Date().toISOString();
        chat.created_at = chat.created_at || chat.updated_at;

        return chatTx.store.put(chat);
      }),
      chatTx.done
    ]);

    // Validate and filter messages
    const validMessages = messages.filter(message => {
      if (!message || !message.id) {
        console.warn('Skipping invalid message (missing ID):', message);
        return false;
      }
      return true;
    });

    // Add all valid messages
    const messageTx = db.transaction('messages', 'readwrite');
    await Promise.all([
      ...validMessages.map(message => {
        // Ensure required fields have values
        message.content = message.content || '';
        message.chat_id = message.chat_id || 'unknown-chat';
        message.sender_id = message.sender_id || 'unknown-sender';
        message.timestamp = message.timestamp || new Date().toISOString();
        message.created_at = message.created_at || message.timestamp;
        message.read = message.read !== undefined ? message.read : false;

        return messageTx.store.put(message);
      }),
      messageTx.done
    ]);

    console.log(`Successfully initialized IndexedDB with ${validChats.length} chats and ${validMessages.length} messages`);
  } catch (error) {
    console.error('Error initializing IndexedDB with mock data:', error);
    throw error;
  }
};
