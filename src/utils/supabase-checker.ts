import { supabase } from '@/lib/supabase';

/**
 * Utility function to check if Supabase connection is working
 * and if the required tables exist
 */
export const checkSupabaseConnection = async (): Promise<{
  connected: boolean;
  tablesExist: {
    profiles: boolean;
    chats: boolean;
    messages: boolean;
    chat_participants: boolean;
    user_status: boolean;
  };
  error?: any;
  details?: string;
}> => {
  try {
    console.log('Checking Supabase connection with URL:', supabase.supabaseUrl);

    // First, try a simple health check
    try {
      const response = await fetch(`${supabase.supabaseUrl}/rest/v1/`);
      console.log('Supabase health check response:', response.status, response.statusText);

      if (!response.ok) {
        return {
          connected: false,
          tablesExist: {
            profiles: false,
            chats: false,
            messages: false,
            chat_participants: false,
            user_status: false,
          },
          error: { status: response.status, statusText: response.statusText },
          details: `Failed to connect to Supabase: ${response.status} ${response.statusText}`
        };
      }
    } catch (fetchError) {
      console.error('Error during Supabase health check:', fetchError);
      return {
        connected: false,
        tablesExist: {
          profiles: false,
          chats: false,
          messages: false,
          chat_participants: false,
          user_status: false,
        },
        error: fetchError,
        details: `Network error during Supabase health check: ${fetchError.message}`
      };
    }

    // Try a simple query instead of RPC
    const { data: testData, error: testError } = await supabase
      .from('_test_connection')
      .select('*')
      .limit(1)
      .catch(e => {
        console.log('Caught error in test query:', e);
        return { data: null, error: e };
      });

    console.log('Test query result:', { data: testData, error: testError });

    // If we get a "relation does not exist" error, that's actually good - it means we connected
    const isConnected = !testError ||
                        (testError.message && testError.message.includes('relation') &&
                         testError.message.includes('does not exist'));

    if (!isConnected) {
      console.error('Error connecting to Supabase:', testError);
      return {
        connected: false,
        tablesExist: {
          profiles: false,
          chats: false,
          messages: false,
          chat_participants: false,
          user_status: false,
        },
        error: testError,
        details: `Failed to query Supabase: ${testError?.message || 'Unknown error'}`
      };
    }

    // Check if tables exist
    const tables = ['profiles', 'chats', 'messages', 'chat_participants', 'user_status'];
    const tableStatus: Record<string, boolean> = {};
    const tableErrors: Record<string, any> = {};

    for (const table of tables) {
      try {
        console.log(`Checking if table '${table}' exists...`);

        // Try to get a single row from each table
        const { error } = await supabase
          .from(table)
          .select('*')
          .limit(1)
          .catch(e => {
            console.log(`Caught error checking table ${table}:`, e);
            return { data: null, error: e };
          });

        // If there's a permission error, the table might exist but we don't have access
        // If there's a "relation does not exist" error, the table doesn't exist
        const tableExists = !error ||
                           (error.message &&
                            !(error.message.includes('relation') &&
                              error.message.includes('does not exist')));

        console.log(`Table '${table}' exists: ${tableExists}`, error ? `Error: ${error.message}` : '');

        tableStatus[table] = tableExists;
        if (error) tableErrors[table] = error;
      } catch (tableError) {
        console.error(`Error checking table ${table}:`, tableError);
        tableStatus[table] = false;
        tableErrors[table] = tableError;
      }
    }

    const allTablesExist = Object.values(tableStatus).every(exists => exists);
    console.log('All tables exist:', allTablesExist);

    return {
      connected: true,
      tablesExist: {
        profiles: tableStatus.profiles,
        chats: tableStatus.chats,
        messages: tableStatus.messages,
        chat_participants: tableStatus.chat_participants,
        user_status: tableStatus.user_status,
      },
      details: allTablesExist
        ? 'Successfully connected to Supabase and all required tables exist'
        : `Connected to Supabase but some tables are missing: ${Object.entries(tableStatus)
            .filter(([_, exists]) => !exists)
            .map(([table]) => table)
            .join(', ')}`
    };
  } catch (error) {
    console.error('Error checking Supabase connection:', error);
    return {
      connected: false,
      tablesExist: {
        profiles: false,
        chats: false,
        messages: false,
        chat_participants: false,
        user_status: false,
      },
      error,
      details: `Unexpected error checking Supabase connection: ${error?.message || 'Unknown error'}`
    };
  }
};

/**
 * Utility function to check if the current user has the necessary permissions
 */
export const checkUserPermissions = async (): Promise<{
  canRead: boolean;
  canWrite: boolean;
  error?: any;
  details?: string;
  auth?: {
    isAuthenticated: boolean;
    user?: any;
  };
}> => {
  try {
    console.log('Checking user permissions...');

    // First check if user is authenticated
    const { data: { user }, error: authError } = await supabase.auth.getUser();

    console.log('Auth user:', user ? 'Authenticated' : 'Not authenticated', authError ? `Error: ${authError.message}` : '');

    if (authError) {
      console.error('Error getting authenticated user:', authError);
      return {
        canRead: false,
        canWrite: false,
        error: authError,
        details: `Error getting authenticated user: ${authError.message}`,
        auth: {
          isAuthenticated: false
        }
      };
    }

    const isAuthenticated = !!user;

    // Check if user can read from messages table
    console.log('Checking read permissions...');
    const { error: readError } = await supabase
      .from('messages')
      .select('*')
      .limit(1)
      .catch(e => {
        console.log('Caught error checking read permissions:', e);
        return { data: null, error: e };
      });

    console.log('Read permission check result:', readError ? `Error: ${readError.message}` : 'Success');

    // Check if user can write to messages table
    // We'll just prepare the query but not execute it
    console.log('Checking write permissions...');
    const { error: writeError } = await supabase
      .from('messages')
      .insert([
        {
          id: 'test-permission-id',
          content: 'Test message',
          chat_id: 'test-chat-id',
          user_id: user?.id || 'test-user-id',
          created_at: new Date().toISOString(),
        },
      ])
      .select()
      .abortSignal(new AbortController().signal) // This will abort the request
      .catch(e => {
        console.log('Caught error checking write permissions:', e);
        return { data: null, error: e };
      });

    console.log('Write permission check result:', writeError ? `Error: ${writeError.message}` : 'Success');

    const canRead = !readError || readError.code !== 'PGRST301'; // PGRST301 is permission denied
    const canWrite = !writeError || writeError.code !== 'PGRST301';

    return {
      canRead,
      canWrite,
      details: `User ${isAuthenticated ? 'is' : 'is not'} authenticated. Can read: ${canRead}. Can write: ${canWrite}.`,
      auth: {
        isAuthenticated,
        user: user ? { id: user.id, email: user.email } : undefined
      }
    };
  } catch (error) {
    console.error('Error checking user permissions:', error);
    return {
      canRead: false,
      canWrite: false,
      error,
      details: `Unexpected error checking user permissions: ${error?.message || 'Unknown error'}`,
      auth: {
        isAuthenticated: false
      }
    };
  }
};
