export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  status?: 'online' | 'offline';
}

export interface Message {
  id: string;
  content: string;
  timestamp: string;
  sender_id: string;
  chat_id: string;
  read: boolean;
  created_at: string;  // Required to match Supabase schema
  attachments?: Attachment[];
  attachment_url?: string;  // Added to match Supabase schema
  attachment_type?: string;  // Added to match Supabase schema
}

export interface Attachment {
  id: string;
  type: 'image' | 'video' | 'file';
  url: string;
  name: string;
  size?: number;
}

export interface Chat {
  id: string;
  name: string;
  type: 'direct' | 'group';
  last_message?: Message;
  unread_count?: number;
  participants: User[];
  labels?: string[];
  created_at: string;
  updated_at: string;
  is_demo?: boolean;
  is_internal?: boolean;
  is_content?: boolean;
  is_signUp?: boolean;
  support_message?: string;
}

export type ChatLabelType = 'demo' | 'internal' | 'content' | 'signUp';
