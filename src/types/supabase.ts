export type Json =
  | string
  | number
  | boolean
  | null
  | { [key: string]: J<PERSON> | undefined }
  | Json[]

export interface Database {
  public: {
    Tables: {
      profiles: {
        Row: {
          id: string
          mobile: string
          created_at: string
          updated_at: string
          full_name: string | null
          avatar_url: string | null
        }
        Insert: {
          id: string
          mobile: string
          created_at?: string
          updated_at?: string
          full_name?: string | null
          avatar_url?: string | null
        }
        Update: {
          id?: string
          mobile?: string
          created_at?: string
          updated_at?: string
          full_name?: string | null
          avatar_url?: string | null
        }
      }
      chats: {
        Row: {
          id: string
          name: string
          type: 'direct' | 'group'
          created_at: string
          updated_at: string
          created_by: string
        }
        Insert: {
          id?: string
          name: string
          type: 'direct' | 'group'
          created_at?: string
          updated_at?: string
          created_by: string
        }
        Update: {
          id?: string
          name?: string
          type?: 'direct' | 'group'
          created_at?: string
          updated_at?: string
          created_by?: string
        }
      }
      messages: {
        Row: {
          id: string
          content: string
          chat_id: string
          user_id: string
          created_at: string
          read: boolean
        }
        Insert: {
          id?: string
          content: string
          chat_id: string
          user_id: string
          created_at?: string
          read?: boolean
        }
        Update: {
          id?: string
          content?: string
          chat_id?: string
          user_id?: string
          created_at?: string
          read?: boolean
        }
      }
      chat_participants: {
        Row: {
          id: string
          chat_id: string
          user_id: string
          joined_at: string
        }
        Insert: {
          id?: string
          chat_id: string
          user_id: string
          joined_at?: string
        }
        Update: {
          id?: string
          chat_id?: string
          user_id?: string
          joined_at?: string
        }
      }
    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
