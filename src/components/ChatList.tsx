import React, { useState, useEffect } from 'react';
import { Chat, User, Message } from '@/types';
import { formatDate } from '@/utils/date-utils';
import { mockUsers } from '@/utils/mock-data';
import { FiSearch, FiCircle, FiX } from 'react-icons/fi';
import { BsCheck2, BsChevronDown, BsFilter, BsPlus, BsPeopleFill, BsPersonFill, BsCircleFill } from 'react-icons/bs';
import { TiTick } from 'react-icons/ti';
import { MdCreateNewFolder, MdClose } from 'react-icons/md';
import { IoRefreshOutline } from 'react-icons/io5';
import { AiOutlineQuestionCircle } from 'react-icons/ai';
import { supabase } from '@/lib/supabase';
import { useChat } from '@/lib/chat-context';
import * as idbService from '@/utils/idb-service';

interface ChatListProps {
  chats: Chat[];
  selectedChat: Chat | null;
  onSelectChat: (chat: Chat) => void;
}

const ChatList: React.FC<ChatListProps> = ({ chats: propsChats, selectedChat, onSelectChat }) => {
  const [localChats, setLocalChats] = useState<Chat[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [showNewConversationModal, setShowNewConversationModal] = useState(false);
  const [showUserSelectionModal, setShowUserSelectionModal] = useState(false);
  const [showGroupCreationModal, setShowGroupCreationModal] = useState(false);
  const [newChatName, setNewChatName] = useState('');
  const [isCreating, setIsCreating] = useState(false);
  const [errorMessage, setErrorMessage] = useState('');
  const [groupParticipants, setGroupParticipants] = useState<User[]>([]);
  const [availableUsers, setAvailableUsers] = useState<User[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [selectedUser, setSelectedUser] = useState<User | null>(null);
  const { currentUser, onlineUsers, messages, supabaseStatus, addChat } = useChat();

  // Function to calculate unread messages for a chat
  const getUnreadCount = (chatId: string): number => {
    return messages.filter(m =>
      m.chat_id === chatId &&
      m.sender_id !== currentUser.id &&
      !m.read
    ).length;
  };

  // Function to check if any participant in a chat is online
  const hasOnlineParticipants = (chat: Chat): boolean => {
    return chat.participants.some(p => onlineUsers.has(p.id) && p.id !== currentUser.id);
  };

  // No need for custom interface since Chat already has last_message

  // Fetch chats and latest messages from Supabase
  useEffect(() => {
    const fetchChats = async () => {
      setIsLoading(true);
      try {
        console.log('Fetching chats from Supabase...');
        const { data, error } = await supabase
          .from('chats')
          .select('*')
          .order('updated_at', { ascending: false });

        if (error) {
          console.error('Error fetching chats:', error);
          return;
        }

        console.log('Chats fetched successfully:', data);

        // Fetch latest message for each chat
        const chatsWithMessages: Chat[] = [];

        for (const chat of data) {
          const { data: messagesData, error: messagesError } = await supabase
            .from('messages')
            .select('*')
            .eq('chat_id', chat.id)
            .order('created_at', { ascending: false })
            .limit(1);

          if (messagesError) {
            console.error(`Error fetching messages for chat ${chat.id}:`, messagesError);
          }

          const chatWithMessage = {
            ...chat,
            last_message: messagesData && messagesData.length > 0 ? messagesData[0] : undefined
          };

          chatsWithMessages.push(chatWithMessage);
        }

        setLocalChats(chatsWithMessages);
      } catch (error) {
        console.error('Error in fetchChats:', error);
      } finally {
        setIsLoading(false);
      }
    };

    fetchChats();
  }, []);

  const openNewConversation = () => {
    setShowNewConversationModal(true);
  };

  const handleChatWithSomeone = () => {
    setShowNewConversationModal(false);
    setShowUserSelectionModal(true);
    fetchUsers();
  };

  const handleCreateGroupChat = () => {
    setShowNewConversationModal(false);
    setShowGroupCreationModal(true);
    fetchUsers();
  };

  const handleSelectUser = (user: User) => {
    setSelectedUser(user);
    createDirectChat(user);
  };

  // Reset states when modals are closed
  useEffect(() => {
    if (!showGroupCreationModal) {
      setGroupParticipants([]);
    }

    if (!showUserSelectionModal && !showGroupCreationModal) {
      setSearchQuery('');
      setSelectedUser(null);
    }
  }, [showUserSelectionModal, showGroupCreationModal]);

  const fetchUsers = async () => {
    try {
      // First try to fetch from Supabase
      const { data, error } = await supabase
        .from('profiles')
        .select('*');

      if (error) {
        console.error('Error fetching users from Supabase:', error);
        // Fall back to mock data
        const mockUsersList = mockUsers.filter((user: User) => user.id !== currentUser.id);
        setAvailableUsers(mockUsersList);
      } else if (data && data.length > 0) {
        // Filter out the current user
        const filteredUsers = data
          .filter((user: any) => user.id !== currentUser.id)
          .map((user: any) => ({
            id: user.id,
            name: user.full_name || 'Unknown',
            email: '',  // May not be available in the profiles table
            avatar: user.avatar_url || undefined,
            status: 'online' as const  // Default to online for simplicity
          }));
        setAvailableUsers(filteredUsers);
      } else {
        // Fallback to mock data if no users found
        const mockUsersList = mockUsers.filter((user: User) => user.id !== currentUser.id);
        setAvailableUsers(mockUsersList);
      }
    } catch (error) {
      console.error('Error in fetchUsers:', error);
      // Fallback to mock data
      const mockUsersList = mockUsers.filter((user: User) => user.id !== currentUser.id);
      setAvailableUsers(mockUsersList);
    }
  };

  const toggleUserSelection = (user: User) => {
    setGroupParticipants(prev => {
      // Check if user is already selected
      const isSelected = prev.some(p => p.id === user.id);

      // Remove if selected, add if not
      return isSelected
        ? prev.filter(p => p.id !== user.id)
        : [...prev, user];
    });
  };

  const closeAllModals = () => {
    setShowNewConversationModal(false);
    setShowUserSelectionModal(false);
    setShowGroupCreationModal(false);
    setNewChatName('');
    setErrorMessage('');
    setGroupParticipants([]);
    setSearchQuery('');
    setSelectedUser(null);
  };

  const createNewGroupChat = async () => {
    if (!newChatName.trim()) {
      setErrorMessage('Please enter a group name');
      return;
    }

    if (groupParticipants.length === 0) {
      setErrorMessage('Please select at least one participant');
      return;
    }

    setIsCreating(true);
    setErrorMessage('');

    try {
      console.log('Creating new group chat with participants:', groupParticipants);

      const chatId = crypto.randomUUID();
      const now = new Date().toISOString();
      const chatName = newChatName.trim();

      // Create new group chat object
      const newChat: Chat = {
        id: chatId,
        name: chatName,
        type: 'group',
        created_at: now,
        updated_at: now,
        participants: [currentUser, ...groupParticipants]
      };

      // Create the initial welcome message
      const messageId = crypto.randomUUID();
      const welcomeMessage = `Group "${chatName}" created with ${groupParticipants.length} participants`;
      const initialMessage: Message = {
        id: messageId,
        content: welcomeMessage,
        sender_id: currentUser.id,
        chat_id: chatId,
        timestamp: now,
        read: true,
        created_at: now
      };

      // Add last_message to the chat
      newChat.last_message = initialMessage;

      // First try to save to Supabase if connected
      let savedToSupabase = false;
      if (supabaseStatus.connected &&
          supabaseStatus.tablesExist?.chats &&
          supabaseStatus.permissions?.canWrite) {
        try {
          console.log('Saving group chat to Supabase...');

          // 1. Insert into chats table
          const { data: chatData, error: chatError } = await supabase
            .from('chats')
            .insert({
              id: chatId,
              name: chatName,
              type: 'group',
              created_at: now,
              updated_at: now,
              created_by: currentUser.id
            })
            .select()
            .single();

          if (chatError) {
            console.error('Error creating group chat in Supabase:', chatError);
            throw new Error(`Error creating group chat: ${chatError.message}`);
          }

          console.log('Group chat created successfully in Supabase:', chatData);

          // 2. Add participants if the chat_participants table exists
          if (supabaseStatus.tablesExist?.chat_participants) {
            try {
              const allParticipants = [currentUser, ...groupParticipants];

              // Add all participants
              for (const participant of allParticipants) {
                await supabase.from('chat_participants').insert({
                  chat_id: chatId,
                  user_id: participant.id,
                  joined_at: now
                });
              }

              console.log('Group participants added to Supabase');
            } catch (participantError) {
              console.error('Error adding group participants:', participantError);
              // Continue even if this fails
            }
          }

          // 3. Create the initial message
          const { data: messageData, error: messageError } = await supabase
            .from('messages')
            .insert({
              id: messageId,
              chat_id: chatId,
              content: welcomeMessage,
              user_id: currentUser.id, // Use user_id for Supabase
              created_at: now,
              read: true
            })
            .select();

          if (messageError) {
            console.error('Error creating initial group message in Supabase:', messageError);
            // Continue even if message creation fails
          } else {
            console.log('Initial group message created in Supabase:', messageData);
          }

          savedToSupabase = true;
        } catch (supabaseError) {
          console.error('Error saving group chat to Supabase:', supabaseError);
          // Continue with local storage as fallback
        }
      }

      // If not saved to Supabase, store locally
      if (!savedToSupabase) {
        console.log('Saving group chat locally...');

        // Store all participants in localStorage
        try {
          const allParticipants = [currentUser, ...groupParticipants];
          const chatParticipants = JSON.parse(localStorage.getItem('chatParticipants') || '{}');

          if (!chatParticipants[chatId]) {
            chatParticipants[chatId] = [];
          }

          // Add all participants
          for (const participant of allParticipants) {
            chatParticipants[chatId].push({
              user_id: participant.id,
              joined_at: now
            });

            // Also add this chat to the user's chat list
            const userChats = JSON.parse(localStorage.getItem('userChats') || '{}');
            if (!userChats[participant.id]) {
              userChats[participant.id] = [];
            }
            userChats[participant.id].push(chatId);
            localStorage.setItem('userChats', JSON.stringify(userChats));
          }

          localStorage.setItem('chatParticipants', JSON.stringify(chatParticipants));
          console.log('Group participants stored locally');
        } catch (storageError) {
          console.error('Error storing group participants locally:', storageError);
        }

        // Add message to local storage
        try {
          const chatMessages = JSON.parse(localStorage.getItem(`messages_${chatId}`) || '[]');
          chatMessages.push(initialMessage);
          localStorage.setItem(`messages_${chatId}`, JSON.stringify(chatMessages));
          console.log('Initial group message stored locally');
        } catch (storageError) {
          console.error('Error storing initial message locally:', storageError);
        }

        // Store in IndexedDB
        try {
          await idbService.addChat(newChat);
          await idbService.addMessage(initialMessage);
          console.log('Group chat and message saved to IndexedDB');
        } catch (dbError) {
          console.error('Error saving group chat to IndexedDB:', dbError);
        }
      }

      // Update local state
      setLocalChats(prevChats => [newChat, ...prevChats]);

      // Add the chat to the global state in ChatContext
      addChat(newChat);
      console.log('Added group chat to global state, now selecting it');

      // Select the new chat
      onSelectChat(newChat);

      closeAllModals();
    } catch (error) {
      console.error('Error creating group chat:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setIsCreating(false);
    }
  };

  const createDirectChat = async (targetUser: User) => {
    setIsCreating(true);
    setErrorMessage('');

    try {
      console.log('Creating direct chat with user:', targetUser);

      const chatId = crypto.randomUUID();
      const now = new Date().toISOString();
      const chatName = targetUser.name;

      // Create new chat object
      const newChat: Chat = {
        id: chatId,
        name: chatName,
        type: 'direct',
        created_at: now,
        updated_at: now,
        participants: [currentUser, targetUser]
      };

      // Create initial message
      const messageId = crypto.randomUUID();
      const initialMessage: Message = {
        id: messageId,
        content: "Chat created",
        sender_id: currentUser.id,
        chat_id: chatId,
        timestamp: now,
        read: true,
        created_at: now
      };

      // Add last_message to the chat
      newChat.last_message = initialMessage;

      // First try to save to Supabase if connected
      let savedToSupabase = false;
      if (supabaseStatus.connected &&
          supabaseStatus.tablesExist?.chats &&
          supabaseStatus.permissions?.canWrite) {
        try {
          console.log('Saving direct chat to Supabase...');

          // 1. Insert into chats table
          const { data: chatData, error: chatError } = await supabase
            .from('chats')
            .insert({
              id: chatId,
              name: chatName,
              type: 'direct',
              created_at: now,
              updated_at: now,
              created_by: currentUser.id
            })
            .select()
            .single();

          if (chatError) {
            console.error('Error creating chat in Supabase:', chatError);
            throw new Error(`Error creating chat: ${chatError.message}`);
          }

          console.log('Chat created successfully in Supabase:', chatData);

          // 2. Add participants if the chat_participants table exists
          if (supabaseStatus.tablesExist?.chat_participants) {
            try {
              // Add current user as participant
              await supabase.from('chat_participants').insert({
                chat_id: chatId,
                user_id: currentUser.id,
                joined_at: now
              });

              // Add target user as participant
              await supabase.from('chat_participants').insert({
                chat_id: chatId,
                user_id: targetUser.id,
                joined_at: now
              });

              console.log('Chat participants added to Supabase');
            } catch (participantError) {
              console.error('Error adding chat participants:', participantError);
              // Continue even if this fails
            }
          }

          // 3. Create the initial message
          const { data: messageData, error: messageError } = await supabase
            .from('messages')
            .insert({
              id: messageId,
              chat_id: chatId,
              content: "Chat created",
              user_id: currentUser.id, // Use user_id for Supabase
              created_at: now,
              read: true
            })
            .select();

          if (messageError) {
            console.error('Error creating initial message in Supabase:', messageError);
            // Continue even if message creation fails
          } else {
            console.log('Initial message created in Supabase:', messageData);
          }

          savedToSupabase = true;
        } catch (supabaseError) {
          console.error('Error saving to Supabase:', supabaseError);
          // Continue with local storage as fallback
        }
      }

      // If not saved to Supabase, store locally
      if (!savedToSupabase) {
        console.log('Saving direct chat locally...');

        // Store chat-user relationship in localStorage
        try {
          // For current user
          const userChats = JSON.parse(localStorage.getItem('userChats') || '{}');
          if (!userChats[currentUser.id]) {
            userChats[currentUser.id] = [];
          }
          userChats[currentUser.id].push(chatId);

          // For target user
          if (!userChats[targetUser.id]) {
            userChats[targetUser.id] = [];
          }
          userChats[targetUser.id].push(chatId);

          localStorage.setItem('userChats', JSON.stringify(userChats));
          console.log('User-chat relationship stored locally');
        } catch (storageError) {
          console.error('Error storing chat relationship locally:', storageError);
        }

        // Store messages in localStorage
        try {
          const chatMessages = JSON.parse(localStorage.getItem(`messages_${chatId}`) || '[]');
          chatMessages.push(initialMessage);
          localStorage.setItem(`messages_${chatId}`, JSON.stringify(chatMessages));
        } catch (storageError) {
          console.error('Error storing message locally:', storageError);
        }

        // Store in IndexedDB
        try {
          await idbService.addChat(newChat);
          await idbService.addMessage(initialMessage);
          console.log('Chat and message saved to IndexedDB');
        } catch (dbError) {
          console.error('Error saving to IndexedDB:', dbError);
        }
      }

      // Update local state
      setLocalChats(prevChats => [newChat, ...prevChats]);

      // Add the chat to the global state in ChatContext
      addChat(newChat);
      console.log('Added chat to global state, now selecting it');

      // Select the new chat
      onSelectChat(newChat);

      closeAllModals();
    } catch (error) {
      console.error('Error creating direct chat:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setIsCreating(false);
    }
  };

  const createNewChat = async () => {
    if (!newChatName.trim()) {
      setErrorMessage('Please enter a chat name');
      return;
    }

    setIsCreating(true);
    setErrorMessage('');

    try {
      console.log('Creating new chat with current user:', currentUser);

      const chatId = crypto.randomUUID();
      const now = new Date().toISOString();

      console.log('Inserting into Supabase chats table...');

      // 1. Insert into chats table
      const { data: chatData, error: chatError } = await supabase
        .from('chats')
        .insert({
          id: chatId,
          name: newChatName.trim(),
          type: 'direct',
          created_at: now,
          updated_at: now,
          created_by: currentUser.id
        })
        .select()
        .single();

      if (chatError) {
        console.error('Error creating chat:', chatError);
        throw new Error(`Error creating chat: ${chatError.message}`);
      }

      console.log('Chat created successfully in Supabase:', chatData);

      // Commenting out participant addition since the table might not exist
      console.log('Skipping participant addition - table might not exist');

      // Store participant relationship directly in chat metadata or in local storage temporarily
      // until the backend is fully set up
      try {
        // Store chat-user relationship in localStorage as a fallback
        const userChats = JSON.parse(localStorage.getItem('userChats') || '{}');
        if (!userChats[currentUser.id]) {
          userChats[currentUser.id] = [];
        }
        userChats[currentUser.id].push(chatId);
        localStorage.setItem('userChats', JSON.stringify(userChats));

        console.log('User-chat relationship stored locally');
      } catch (storageError) {
        console.error('Error storing chat relationship locally:', storageError);
        // Continue even if local storage fails
      }

      // 3. Create the initial message
      console.log('Creating initial message...');
      const { data: messageData, error: messageError } = await supabase
        .from('messages')
        .insert({
          chat_id: chatId,
          content: "Chat created",
          sender_id: currentUser.id,
          created_at: now,
          read: true
        })
        .select();

      if (messageError) {
        console.error('Error creating initial message:', messageError);
        // Continue even if message creation fails
      } else {
        console.log('Initial message created:', messageData);
      }

      // Give Supabase a moment to process everything
      await new Promise(resolve => setTimeout(resolve, 500));

      // Fetch the newly created chat
      const { data: newChatData, error: fetchError } = await supabase
        .from('chats')
        .select('*')
        .eq('id', chatId)
        .single();

      if (fetchError) {
        console.error('Error fetching the new chat:', fetchError);
      } else {
        console.log('New chat fetched:', newChatData);
        setLocalChats(prevChats => [newChatData, ...prevChats]);

        // Add the chat to the global state in ChatContext
        addChat(newChatData);
        console.log('Added new chat to global state');

        // Select the new chat
        onSelectChat(newChatData);
      }
      closeAllModals();
    } catch (error) {
      console.error('Error creating chat:', error);
      setErrorMessage(error instanceof Error ? error.message : 'Unknown error occurred');
    } finally {
      setIsCreating(false);
    }
  };
  return (
    <div className="flex flex-col h-full w-92 border-r border-gray-200 relative">
      {/* Header with chats label and buttons */}
      <div className="py-3 px-4 border-b border-gray-200 flex items-center justify-between">
        <div className="flex items-center">
          <span className="text-gray-500 text-sm">chats</span>
          <button className="ml-3 text-gray-500">
            <TiTick className="w-4 h-4" />
          </button>
        </div>
        <div className="flex items-center space-x-3">
          <button className="text-gray-500">
            <BsFilter className="w-4 h-4" />
          </button>
          <button className="text-gray-500 hover:text-gray-700">
            <IoRefreshOutline className="w-4 h-4" />
          </button>
          <button className="text-gray-500 hover:text-gray-700">
            <AiOutlineQuestionCircle className="w-4 h-4" />
          </button>
          <div className="flex items-center space-x-1 ml-1">
            <span className="text-xs text-gray-500">5 / 6 phones</span>
            <button className="text-gray-500 hover:text-gray-700">
              <BsChevronDown className="w-3 h-3" />
            </button>
          </div>
        </div>
      </div>

      {/* Custom filter area */}
      <div className="flex items-center justify-between px-4 py-2 border-b border-gray-200">
        <div className="flex items-center space-x-2">
          <span className="flex items-center">
            <span className="bg-green-500 w-4 h-4 inline-flex items-center justify-center rounded-sm text-white mr-1">
              <MdCreateNewFolder className="w-3 h-3" />
            </span>
            <span className="text-xs font-medium">Custom filter</span>
          </span>
          <button className="text-xs bg-gray-200 rounded px-2 py-0.5">Save</button>
        </div>
        <div className="flex items-center">
          <div className="relative">
            <FiSearch className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-500 w-3 h-3" />
            <input
              type="text"
              placeholder="Search"
              className="pl-7 pr-2 py-1 bg-gray-100 border-none outline-none text-xs w-24 rounded-md"
            />
          </div>
          <div className="flex items-center ml-2 text-xs">
            <button className="inline-flex items-center bg-gray-100 rounded-md px-2 py-1 text-green-600 font-medium">
              <span>Filtered</span>
              <BsChevronDown className="w-3 h-3 ml-1" />
            </button>
          </div>
        </div>
      </div>

      {/* Chat list */}
      <div className="flex-1 overflow-y-auto">
        {isLoading ? (
          <div className="flex justify-center items-center h-20 text-gray-500">
            Loading chats...
          </div>
        ) : localChats.length === 0 ? (
          <div className="flex justify-center items-center h-20 text-gray-500">
            No chats found. Create a new chat to get started.
          </div>
        ) : (
          localChats.map((chat) => (
          <div
            key={chat.id}
            onClick={() => onSelectChat(chat)}
            className={`flex py-3 px-4 border-b border-gray-200 hover:bg-gray-50 cursor-pointer relative ${selectedChat?.id === chat.id ? 'bg-gray-50' : ''}`}
          >
            {/* Avatar */}
            <div className="h-10 w-10 rounded-full bg-gray-300 flex-shrink-0 flex items-center justify-center overflow-hidden text-white relative">
              {/* Online indicator */}
              {hasOnlineParticipants(chat) && (
                <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
              )}
              {chat.name === 'Test El Centro' ? (
                <span className="text-lg font-semibold">T</span>
              ) : chat.name === 'Test Skope Final 5' ? (
                <span className="text-lg font-semibold">T</span>
              ) : chat.name === 'Periskope Team Chat' ? (
                <span className="text-lg text-white bg-green-500 rounded-full w-full h-full flex items-center justify-center">P</span>
              ) : chat.name === '+91 99999 99999' ? (
                <span className="text-lg font-semibold">+</span>
              ) : chat.name === 'Test Demo17' ? (
                <span className="text-lg font-semibold">T</span>
              ) : chat.name === 'Testing group' ? (
                <span className="text-lg font-semibold">T</span>
              ) : chat.name === 'Yasin 3' ? (
                <span className="text-lg font-semibold">Y</span>
              ) : chat.name === 'Test Skope Final 9473' ? (
                <span className="text-lg font-semibold">T</span>
              ) : chat.name === 'Skope Demo' ? (
                <span className="text-lg font-semibold">S</span>
              ) : (
                <span className="text-lg font-semibold">T</span>
              )}
            </div>

            {/* Chat info */}
            <div className="ml-3 flex-1 min-w-0 relative">
              <div className="flex items-center justify-between">
                <h3 className="font-medium text-sm truncate">{chat.name}</h3>
                <span className="text-[10px] text-gray-500 flex-shrink-0">
                  {formatDate(chat.updated_at)}
                </span>
              </div>

              <div className="flex items-center mt-1">
                <p className="text-[11px] text-gray-500 truncate">
                  {chat.last_message ? chat.last_message.content : 'No messages yet'}
                </p>
              </div>

              {chat.id === '10' && (
                <div className="flex items-center mt-1">
                  <p className="text-[11px] text-gray-500 truncate">
                    ✓ test 123
                  </p>
                </div>
              )}

              {chat.id === '11' && (
                <div className="flex items-center mt-1">
                  <p className="text-[11px] text-gray-500 truncate">
                    ✓ test 123
                  </p>
                </div>
              )}

              {/* Chat label */}
              <div className="flex items-center mt-1 space-x-1">
                {chat.is_demo && (
                  <span className="text-[10px] bg-gray-100 text-gray-600 px-1.5 py-0.5 rounded">Demo</span>
                )}
                {chat.is_internal && (
                  <span className="text-[10px] bg-green-100 text-green-600 px-1.5 py-0.5 rounded">Internal</span>
                )}
                {chat.is_content && (
                  <span className="text-[10px] bg-gray-100 text-gray-600 px-1.5 py-0.5 rounded">Content</span>
                )}
                {chat.is_signUp && (
                  <span className="text-[10px] bg-green-100 text-green-600 px-1.5 py-0.5 rounded">SignUp</span>
                )}

                {/* Unread indicator */}
                {getUnreadCount(chat.id) > 0 && (
                  <div className="absolute right-2 top-1/2 transform -translate-y-1/2 w-5 h-5 rounded-full bg-green-500 text-white flex items-center justify-center text-xs">
                    {getUnreadCount(chat.id)}
                  </div>
                )}
              </div>
            </div>
          </div>
        )))
        }
      </div>

      {/* Floating Action Button - Now on the left side */}
      <div className="fixed bottom-6 left-6">
        <button
          onClick={openNewConversation}
          className="w-14 h-14 rounded-full shadow-lg flex items-center justify-center bg-green-500 text-white hover:bg-green-600 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-green-500"
          aria-label="New conversation"
        >
          <svg xmlns="http://www.w3.org/2000/svg" className="h-8 w-8" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="3" strokeLinecap="round" strokeLinejoin="round">
            <line x1="12" y1="5" x2="12" y2="19"></line>
            <line x1="5" y1="12" x2="19" y2="12"></line>
          </svg>
        </button>
      </div>

      {/* New Conversation Modal */}
      {showNewConversationModal && (<>
        <div className="fixed inset-0 bg-black/50" onClick={closeAllModals}></div>
        <div className="bg-white w-[320px] rounded-lg p-4 relative mt-16 mr-4 shadow-lg">
          <h2 className="text-lg font-medium text-center mb-4">START NEW CHAT</h2>

          <div className="flex flex-col space-y-3">
            <button
              onClick={handleChatWithSomeone}
              className="flex items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
            >
              <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                <BsPersonFill className="text-green-500 text-base" />
              </div>
              <span className="text-base text-gray-700">Start new chat</span>
            </button>

            <button
              onClick={handleCreateGroupChat}
              className="flex items-center p-3 bg-green-50 rounded-lg hover:bg-green-100 transition-colors"
            >
              <div className="w-8 h-8 rounded-full bg-green-100 flex items-center justify-center mr-3">
                <BsPeopleFill className="text-green-500 text-base" />
              </div>
              <span className="text-base text-gray-700">Create group chat</span>
            </button>
          </div>

          <button
            onClick={closeAllModals}
            className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
          >
            <FiX className="w-5 h-5" />
          </button>
        </div>
        </>)}

      {/* User Selection Modal */}
      {showUserSelectionModal && (
        <>
          <div className="fixed inset-0 bg-black/30" onClick={closeAllModals} />
          <div className="fixed right-4 top-16 w-64 bg-white rounded-lg shadow-lg overflow-hidden z-50 border border-gray-200">
            <div className="p-3 border-b border-gray-200">
              <div className="flex justify-between items-center">
                <h2 className="text-sm font-medium">Start a New Chat</h2>
                <button
                  onClick={closeAllModals}
                  className="text-gray-500 hover:text-gray-700"
                >
                  <FiX className="w-4 h-4" />
                </button>
              </div>
            </div>

            {errorMessage && (
              <div className="px-3 py-2 bg-red-50 text-red-700 text-xs">
                {errorMessage}
              </div>
            )}

            {/* User search */}
            <div className="p-3">
              <div className="relative">
                <FiSearch className="absolute left-2 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
                <input
                  type="text"
                  placeholder="Search users..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="w-full pl-8 pr-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-transparent"
                  disabled={isCreating}
                />
              </div>
            </div>

            {/* User list */}
            <div className="max-h-64 overflow-y-auto">
              {availableUsers.length === 0 ? (
                <div className="p-3 text-center text-gray-500 text-sm">
                  No users available
                </div>
              ) : (
                <ul className="divide-y divide-gray-100">
                  {availableUsers
                    .filter(user =>
                      user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                      user.email?.toLowerCase().includes(searchQuery.toLowerCase())
                    )
                    .map(user => (
                      <li
                        key={user.id}
                        className="px-3 py-2 hover:bg-gray-50 cursor-pointer"
                        onClick={() => handleSelectUser(user)}
                      >
                        <div className="flex items-center space-x-2">
                          <div className="h-8 w-8 rounded-full bg-green-100 flex-shrink-0 flex items-center justify-center text-green-500">
                            <span className="text-sm font-medium">{user.name.charAt(0).toUpperCase()}</span>
                          </div>

                          <div className="flex-1 min-w-0">
                            <p className="text-sm font-medium truncate">{user.name}</p>
                            {user.email && (
                              <p className="text-xs text-gray-500 truncate">{user.email}</p>
                            )}
                          </div>
                        </div>
                      </li>
                    ))}
                </ul>
              )}
            </div>

            <div className="p-2 text-center text-xs text-gray-500 border-t border-gray-100">
              Select a user to start a chat
            </div>
          </div>
        </>
      )}

      {showGroupCreationModal && (
  <>
    <div className="fixed inset-0 bg-black/30" onClick={closeAllModals}></div>
    <div className="fixed right-4 top-16 bg-white w-[320px] rounded-lg p-4 shadow-lg z-50 border border-gray-200 overflow-auto max-h-[80vh]">
      <div className="flex justify-between items-center mb-3">
        <h3 className="text-base font-medium">Create New Group</h3>
        <button
          onClick={closeAllModals}
          className="text-gray-500 hover:text-gray-700"
        >
          <FiX className="w-5 h-5" />
        </button>
      </div>

      {errorMessage && (
        <div className="mb-3 p-2 bg-red-50 text-red-700 text-xs rounded-md">
          {errorMessage}
        </div>
      )}

      <div className="mb-3">
        <label htmlFor="groupName" className="block text-sm text-gray-700 mb-1">Group Name</label>
        <input
          id="groupName"
          type="text"
          value={newChatName}
          onChange={(e) => setNewChatName(e.target.value)}
          className="w-full px-3 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-transparent"
          placeholder="Enter group name"
          disabled={isCreating}
        />
      </div>

      {groupParticipants.length > 0 && (
        <div className="mb-3">
          <label className="block text-sm text-gray-700 mb-1">
            Selected Participants ({groupParticipants.length})
          </label>
          <div className="flex flex-wrap gap-1">
            {groupParticipants.map(user => (
              <div
                key={user.id}
                className="flex items-center bg-green-100 text-green-800 px-2 py-0.5 rounded-full text-xs"
              >
                <span>{user.name}</span>
                <button
                  onClick={() => toggleUserSelection(user)}
                  className="ml-1 text-green-600 hover:text-green-800"
                  disabled={isCreating}
                >
                  <FiX className="w-3 h-3" />
                </button>
              </div>
            ))}
          </div>
        </div>
      )}

      <div className="mb-3">
        <label className="block text-sm text-gray-700 mb-1">Add Participants</label>
        <div className="relative">
          <FiSearch className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <input
            type="text"
            placeholder="Search users"
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="w-full pl-9 pr-2 py-1.5 text-sm border border-gray-300 rounded-md focus:outline-none focus:ring-1 focus:ring-green-500 focus:border-transparent"
            disabled={isCreating}
          />
        </div>
      </div>

      <div className="mb-3 max-h-40 overflow-y-auto border border-gray-100 rounded-md">
        {availableUsers.length === 0 ? (
          <div className="p-2 text-center text-gray-500 text-xs">
            No users available
          </div>
        ) : (
          <ul className="divide-y divide-gray-100">
            {availableUsers
              .filter(user =>
                user.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
                user.email?.toLowerCase().includes(searchQuery.toLowerCase())
              )
              .map(user => {
                const isSelected = groupParticipants.some(p => p.id === user.id);
                return (
                  <li
                    key={user.id}
                    className={`px-3 py-2 text-sm flex items-center cursor-pointer hover:bg-gray-50 ${isSelected ? 'bg-green-50' : ''}`}
                    onClick={() => toggleUserSelection(user)}
                  >
                    <div className="h-7 w-7 rounded-full bg-gray-300 flex items-center justify-center text-white text-xs mr-2">
                      {user.avatar ? (
                        <img src={user.avatar} alt={user.name} className="h-full w-full object-cover rounded-full" />
                      ) : (
                        <span>{user.name.charAt(0).toUpperCase()}</span>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <p className="truncate">{user.name}</p>
                      {user.email && <p className="text-xs text-gray-500 truncate">{user.email}</p>}
                    </div>
                    <div className="ml-2">
                      {isSelected ? (
                        <div className="w-4 h-4 rounded-full bg-green-500 flex items-center justify-center">
                          <BsCheck2 className="text-white text-xs" />
                        </div>
                      ) : (
                        <div className="w-4 h-4 rounded-full border border-gray-300" />
                      )}
                    </div>
                  </li>
                );
              })}
          </ul>
        )}
      </div>

      <div className="flex justify-end space-x-2 mt-3">
        <button
          onClick={closeAllModals}
          className="px-3 py-1.5 text-sm text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50"
          disabled={isCreating}
        >
          Cancel
        </button>
        <button
          onClick={createNewGroupChat}
          className="px-3 py-1.5 text-sm bg-green-500 text-white rounded-md hover:bg-green-600 disabled:opacity-70"
          disabled={isCreating || groupParticipants.length === 0 || !newChatName.trim()}
        >
          {isCreating ? 'Creating...' : 'Create'}
        </button>
      </div>
    </div>
  </>
)}

    </div>
  );
};

export default ChatList;
