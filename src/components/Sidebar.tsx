import React from 'react';
import { 
  FiHome, 
  FiMessageSquare, 
  FiLink, 
  FiSettings,
  FiUsers,
  FiClipboard,
  FiCheckSquare
} from 'react-icons/fi';
import { RiFileListLine, RiMegaphoneLine } from 'react-icons/ri';
import { BiBarChartAlt2 } from 'react-icons/bi';
import { BsBriefcase, BsCardImage } from 'react-icons/bs';
import { MdOutlineGroups } from 'react-icons/md';

const Sidebar = () => {
  return (
    <aside className="w-14 bg-white flex flex-col items-center py-4 border-r border-gray-200">
      <div className="mb-6">
        <div className="w-8 h-8 rounded-md bg-green-500 flex items-center justify-center text-white">
          <span className="font-bold text-lg">P</span>
        </div>
      </div>
      
      <nav className="flex flex-col items-center space-y-5">
        <button className="w-8 h-8 rounded-md flex items-center justify-center text-gray-500 hover:bg-gray-100">
          <FiHome className="w-5 h-5" />
        </button>
        <button className="w-8 h-8 rounded-md flex items-center justify-center bg-gray-100 text-green-500">
          <FiMessageSquare className="w-5 h-5" />
        </button>
        <button className="w-8 h-8 rounded-md flex items-center justify-center text-gray-500 hover:bg-gray-100">
          <BsBriefcase className="w-5 h-5" />
        </button>
        <button className="w-8 h-8 rounded-md flex items-center justify-center text-gray-500 hover:bg-gray-100">
          <BiBarChartAlt2 className="w-5 h-5" />
        </button>
        <button className="w-8 h-8 rounded-md flex items-center justify-center text-gray-500 hover:bg-gray-100">
          <RiFileListLine className="w-5 h-5" />
        </button>
        <button className="w-8 h-8 rounded-md flex items-center justify-center text-gray-500 hover:bg-gray-100">
          <RiMegaphoneLine className="w-5 h-5" />
        </button>
        <button className="w-8 h-8 rounded-md flex items-center justify-center text-gray-500 hover:bg-gray-100">
          <MdOutlineGroups className="w-5 h-5" />
        </button>
        <button className="w-8 h-8 rounded-md flex items-center justify-center text-gray-500 hover:bg-gray-100">
          <BsCardImage className="w-5 h-5" />
        </button>
        <button className="w-8 h-8 rounded-md flex items-center justify-center text-gray-500 hover:bg-gray-100">
          <FiCheckSquare className="w-5 h-5" />
        </button>
      </nav>
      
      <div className="mt-auto">
        <button className="w-8 h-8 rounded-md flex items-center justify-center text-gray-500 hover:bg-gray-100">
          <FiSettings className="w-5 h-5" />
        </button>
      </div>
    </aside>
  );
};

export default Sidebar;
