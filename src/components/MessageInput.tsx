import React, { useState, useEffect, useRef } from 'react';
import { FiPaperclip, FiSend } from 'react-icons/fi';
import { BsEmojiSmile, BsImage, BsClock, BsWhatsapp } from 'react-icons/bs';
import { RiAttachmentLine } from 'react-icons/ri';
import { BiMicrophone } from 'react-icons/bi';
import { IoSendSharp } from 'react-icons/io5';
import { useChat } from '@/lib/chat-context';

interface MessageInputProps {
  onSendMessage: (content: string) => void;
  chatId?: string;
}

const MessageInput: React.FC<MessageInputProps> = ({ onSendMessage, chatId }) => {
  const [message, setMessage] = useState('');
  const { setTyping, selectedChat } = useChat();
  const typingTimeoutRef = useRef<NodeJS.Timeout | null>(null);
  const isTypingRef = useRef(false);

  // Handle typing indicator
  const handleTyping = () => {
    const currentChatId = chatId || selectedChat?.id;
    if (!currentChatId) return;

    // If not already typing, send typing indicator
    if (!isTypingRef.current) {
      setTyping(currentChatId, true);
      isTypingRef.current = true;
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Set new timeout to stop typing indicator after 2 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      setTyping(currentChatId, false);
      isTypingRef.current = false;
    }, 2000);
  };

  // Clean up timeout on unmount
  useEffect(() => {
    return () => {
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }

      // Make sure to reset typing status when component unmounts
      const currentChatId = chatId || selectedChat?.id;
      if (currentChatId && isTypingRef.current) {
        setTyping(currentChatId, false);
      }
    };
  }, [chatId, selectedChat, setTyping]);

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    if (message.trim()) {
      onSendMessage(message);
      setMessage('');

      // Reset typing indicator after sending message
      const currentChatId = chatId || selectedChat?.id;
      if (currentChatId && isTypingRef.current) {
        setTyping(currentChatId, false);
        isTypingRef.current = false;
      }
    }
  };

  return (
    <div className="border-t border-gray-200 bg-white py-2 px-3">
      {/* Bottom section with WhatsApp and Private Note */}
      <div className="flex items-center justify-between mb-2">
        <div className="flex items-center text-xs text-gray-500">
          <BsWhatsapp className="w-4 h-4 mr-1 text-green-500" />
          <span>WhatsApp</span>
        </div>
        <div className="flex items-center text-xs text-gray-500">
          <span className="text-orange-400 mr-1">⭐</span>
          <span className="text-orange-400">Private Note</span>
        </div>
      </div>

      {/* Message input form */}
      <form onSubmit={handleSubmit} className="flex items-center">
        <div className="flex-1 border border-gray-100 rounded-md p-2 flex bg-white">
          <input
            type="text"
            placeholder="Message..."
            value={message}
            onChange={(e) => {
              setMessage(e.target.value);
              handleTyping();
            }}
            className="flex-1 outline-none text-sm"
          />

          <div className="flex items-center space-x-3 ml-2">
            <button
              type="button"
              className="text-gray-400 hover:text-gray-600"
            >
              <RiAttachmentLine className="w-5 h-5" />
            </button>
            <button
              type="button"
              className="text-gray-400 hover:text-gray-600"
            >
              <BsEmojiSmile className="w-5 h-5" />
            </button>
            <button
              type="button"
              className="text-gray-400 hover:text-gray-600"
            >
              <BiMicrophone className="w-5 h-5" />
            </button>
            <button
              type="button"
              className="text-gray-400 hover:text-gray-600"
            >
              <BsClock className="w-5 h-5" />
            </button>
          </div>
        </div>

        <button
          type="submit"
          disabled={!message.trim()}
          className={`ml-2 p-2 rounded-full ${message.trim() ? 'bg-green-500 text-white' : 'bg-gray-200 text-gray-400'}`}
        >
          <IoSendSharp className="w-5 h-5" />
        </button>
      </form>

      {/* Footer with logo */}
      <div className="flex justify-end mt-1">
        <div className="flex items-center">
          <span className="text-xs text-green-500 font-medium">Periskope</span>
        </div>
      </div>
    </div>
  );
};

export default MessageInput;
