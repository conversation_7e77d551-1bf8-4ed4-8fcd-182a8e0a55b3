import React, { useEffect, useState } from 'react';
import { supabase } from '@/lib/supabase';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from '@/lib/supabase-config';

interface SupabaseAuthStatusProps {
  showDetails?: boolean;
}

const SupabaseAuthStatus: React.FC<SupabaseAuthStatusProps> = ({ showDetails = false }) => {
  const [status, setStatus] = useState<{
    isConnected: boolean;
    error?: string;
    session?: any;
    url?: string;
    hasAnonKey: boolean;
  }>({
    isConnected: false,
    hasAnonKey: !!SUPABASE_ANON_KEY
  });

  useEffect(() => {
    const checkConnection = async () => {
      try {
        // Check if we have a URL and anon key
        if (!SUPABASE_URL || !SUPABASE_ANON_KEY) {
          setStatus({
            isConnected: false,
            error: 'Missing Supabase URL or anon key',
            url: SUPABASE_URL,
            hasAnonKey: !!SUPABASE_ANON_KEY
          });
          return;
        }

        // Test the connection
        const { data, error } = await supabase.auth.getSession();
        
        if (error) {
          setStatus({
            isConnected: false,
            error: error.message,
            url: SUPABASE_URL,
            hasAnonKey: !!SUPABASE_ANON_KEY
          });
        } else {
          setStatus({
            isConnected: true,
            session: data.session,
            url: SUPABASE_URL,
            hasAnonKey: !!SUPABASE_ANON_KEY
          });
        }
      } catch (err: any) {
        setStatus({
          isConnected: false,
          error: err.message || 'Unknown error',
          url: SUPABASE_URL,
          hasAnonKey: !!SUPABASE_ANON_KEY
        });
      }
    };

    checkConnection();
  }, []);

  return (
    <div className="bg-white rounded-lg shadow-sm p-3 mb-4">
      <div className="flex items-center">
        <div className={`w-3 h-3 rounded-full mr-2 ${status.isConnected ? 'bg-green-500' : 'bg-red-500'}`}></div>
        <span className={`font-medium ${status.isConnected ? 'text-green-500' : 'text-red-500'}`}>
          Supabase: {status.isConnected ? 'Connected' : 'Disconnected'}
        </span>
      </div>
      
      {showDetails && (
        <div className="mt-2 text-sm text-gray-600">
          <div className="grid grid-cols-2 gap-2">
            <div>URL:</div>
            <div className={status.url ? 'text-green-500' : 'text-red-500'}>
              {status.url ? status.url : 'Not set'}
            </div>
            
            <div>API Key:</div>
            <div className={status.hasAnonKey ? 'text-green-500' : 'text-red-500'}>
              {status.hasAnonKey ? 'Set' : 'Not set'}
            </div>
            
            <div>Connection:</div>
            <div className={status.isConnected ? 'text-green-500' : 'text-red-500'}>
              {status.isConnected ? 'Connected' : 'Disconnected'}
            </div>
            
            <div>Session:</div>
            <div className={status.session ? 'text-green-500' : 'text-gray-500'}>
              {status.session ? 'Active' : 'None'}
            </div>
          </div>
          
          {status.error && (
            <div className="mt-2 p-2 bg-red-50 text-red-700 rounded text-xs">
              Error: {status.error}
            </div>
          )}
          
          {!status.isConnected && (
            <div className="mt-2 p-2 bg-yellow-50 text-yellow-700 rounded text-xs">
              <p>To fix connection issues:</p>
              <ol className="list-decimal ml-4 mt-1">
                <li>Create a .env.local file in the project root</li>
                <li>Add your Supabase URL and anon key (from Supabase dashboard)</li>
                <li>Restart the development server</li>
              </ol>
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SupabaseAuthStatus;
