import React, { useEffect, useRef } from 'react';
import { Message, User } from '@/types';
import { formatDate } from '@/utils/date-utils';
import { BsCheck2All, BsCheckAll, BsCircleFill } from 'react-icons/bs';
import { BiCheck } from 'react-icons/bi';
import { useChat } from '@/lib/chat-context';

interface MessagesProps {
  messages: Message[];
  currentUser: User;
  users: User[];
  chatId?: string;
}

const Messages: React.FC<MessagesProps> = ({ messages, currentUser, users = [], chatId }) => {
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const { onlineUsers, typingUsers, selectedChat } = useChat();
  const currentChatId = chatId || selectedChat?.id;

  // Ensure users is always an array and log it for debugging
  const safeUsers = Array.isArray(users) ? users : [];
  console.log('Users array:', safeUsers);

  // Auto-scroll to bottom for new messages
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const getUser = (id?: string) => {
    // Safely handle undefined or null id
    if (!id) return null;

    // Ensure users is an array before calling find
    if (!Array.isArray(safeUsers) || safeUsers.length === 0) {
      // Return a fallback user object if no users are available
      return {
        id,
        name: 'Unknown User',
        email: '',
        status: 'offline' as const
      };
    }

    // Find the user or return a fallback
    return safeUsers.find(user => user && user.id === id) || {
      id,
      name: 'Unknown User',
      email: '',
      status: 'offline' as const
    };
  };

  const isCurrentUser = (senderId?: string) => {
    // Safely handle undefined or null senderId
    if (!senderId || !currentUser) return false;
    return senderId === currentUser.id;
  };

  const isUserOnline = (userId?: string) => {
    if (!userId) return false;
    return onlineUsers.has(userId);
  };

  const isUserTyping = (userId?: string) => {
    if (!userId || !currentChatId) return false;
    return typingUsers.get(userId) === currentChatId;
  };

  return (
    <div className="flex-1 overflow-y-auto px-4 py-6 space-y-6 bg-gray-50">
      {/* Date separators - these would be dynamic based on actual messages */}
      <div className="flex justify-center">
        <div className="bg-gray-200 rounded-full px-3 py-1 text-xs text-gray-600">
          22-01-2025
        </div>
      </div>

      {/* Example messages based on the screenshot */}
      <div className="flex justify-start">
        <div className="flex items-start max-w-[70%]">
          <div className="mr-2 flex-shrink-0">
            <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-white font-semibold">
              R
            </div>
          </div>
          <div className="bg-white rounded-lg p-3 shadow-sm">
            <div className="text-xs font-medium text-gray-600 mb-1">
              Roshang Artel
            </div>
            <div className="text-sm">
              Hello, South Euna!
            </div>
            <div className="flex items-center justify-end mt-1 space-x-1">
              <span className="text-[10px] text-gray-500">10:21</span>
            </div>
          </div>
        </div>
      </div>

      <div className="flex justify-end">
        <div className="max-w-[70%] bg-green-50 rounded-lg p-3 shadow-sm">
          <div className="text-sm text-green-800">
            Hello
          </div>
          <div className="flex items-center justify-end mt-1 space-x-1">
            <span className="text-[10px] text-gray-500">22:07</span>
            <BsCheckAll className="w-3 h-3 text-green-500" />
          </div>
        </div>
      </div>

      {/* Another date separator */}
      <div className="flex justify-center">
        <div className="bg-gray-200 rounded-full px-3 py-1 text-xs text-gray-600">
          23-01-2025
        </div>
      </div>

      {/* Dynamic message rendering */}
      {messages && Array.isArray(messages) && messages.filter(m => m && typeof m === 'object').map((message) => {
        // Only process valid messages
        if (!message || !message.id || !message.content) return null;

        try {
          // Get the sender and check if it's the current user
          const sender = getUser(message.sender_id);
          const isSentByCurrentUser = isCurrentUser(message.sender_id);

          // Use created_at for timestamp if timestamp is not available
          const messageTime = message.timestamp || message.created_at || '';

          if (message.id === '1' || message.id === '2') {
            // Skip the hardcoded messages
            return null;
          }

        return (
          <div
            key={message.id}
            className={`flex ${isSentByCurrentUser ? 'justify-end' : 'justify-start'}`}
          >
            {!isSentByCurrentUser && (
              <div className="flex items-start max-w-[70%]">
                <div className="mr-2 flex-shrink-0">
                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-white font-semibold relative">
                    {sender && typeof sender.name === 'string' ? sender.name.charAt(0) : '?'}
                    {isUserOnline(message.sender_id) && (
                      <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                    )}
                  </div>
                </div>
                <div className="bg-white rounded-lg p-3 shadow-sm">
                  <div className="text-xs font-medium text-gray-600 mb-1">
                    {sender && sender.name ? sender.name : 'Unknown User'}
                  </div>
                  <div className="text-sm">
                    {message.content}
                  </div>
                  <div className="flex items-center justify-end mt-1 space-x-1">
                    <span className="text-[10px] text-gray-500">
                      {messageTime ? formatDate(messageTime) : ''}
                    </span>
                  </div>
                </div>
              </div>
            )}

            {isSentByCurrentUser && (
              <div className="max-w-[70%] bg-green-50 rounded-lg p-3 shadow-sm">
                <div className="text-sm text-green-800">
                  {message.content}
                </div>
                <div className="flex items-center justify-end mt-1 space-x-1">
                  <span className="text-[10px] text-gray-500">
                    {messageTime ? formatDate(messageTime) : ''}
                  </span>
                  <BsCheckAll className={`w-3 h-3 ${message.read ? 'text-green-500' : 'text-gray-400'}`} />
                </div>
              </div>
            )}
          </div>
        );
        } catch (error) {
          console.error('Error rendering message:', error, message);
          return null;
        }
      })}

      {/* Typing indicators */}
      {typingUsers && Array.from(typingUsers.entries()).map(([userId, typingChatId]) => {
        if (typingChatId !== currentChatId) return null;

        try {
          const typingUser = getUser(userId);
          if (!typingUser) return null;

          return (
            <div key={`typing-${userId}`} className="flex justify-start">
              <div className="flex items-start max-w-[70%]">
                <div className="mr-2 flex-shrink-0">
                  <div className="w-8 h-8 bg-gray-300 rounded-full flex items-center justify-center text-white font-semibold relative">
                    {typingUser && typeof typingUser.name === 'string' ? typingUser.name.charAt(0) : '?'}
                    {isUserOnline(userId) && (
                      <div className="absolute bottom-0 right-0 w-3 h-3 bg-green-500 rounded-full border-2 border-white"></div>
                    )}
                  </div>
                </div>
                <div className="bg-white rounded-lg p-3 shadow-sm">
                  <div className="text-xs font-medium text-gray-600 mb-1">
                    {typingUser && typingUser.name ? typingUser.name : 'Unknown User'}
                  </div>
                  <div className="text-sm flex items-center">
                    <span className="typing-indicator">
                      <span className="dot"></span>
                      <span className="dot"></span>
                      <span className="dot"></span>
                    </span>
                  </div>
                </div>
              </div>
            </div>
          );
        } catch (error) {
          console.error('Error rendering typing indicator:', error);
          return null;
        }
      })}

      {/* Add bottom spacing and scroll reference */}
      <div className="h-4" ref={messagesEndRef}></div>

      {/* Add CSS for typing indicator */}
      <style jsx>{`
        .typing-indicator {
          display: flex;
          align-items: center;
        }
        .dot {
          display: inline-block;
          width: 8px;
          height: 8px;
          border-radius: 50%;
          background-color: #9CA3AF;
          margin-right: 4px;
          animation: typing 1.4s infinite ease-in-out;
        }
        .dot:nth-child(1) {
          animation-delay: 0s;
        }
        .dot:nth-child(2) {
          animation-delay: 0.2s;
        }
        .dot:nth-child(3) {
          animation-delay: 0.4s;
        }
        @keyframes typing {
          0%, 60%, 100% {
            transform: translateY(0);
          }
          30% {
            transform: translateY(-6px);
          }
        }
      `}</style>
    </div>
  );
};

export default Messages;
