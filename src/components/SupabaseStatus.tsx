import React from 'react';
import { useChat } from '@/lib/chat-context';
import { BsCheckCircleFill, BsXCircleFill, BsExclamationTriangleFill } from 'react-icons/bs';

interface SupabaseStatusProps {
  showDetails?: boolean;
}

const SupabaseStatus: React.FC<SupabaseStatusProps> = ({ showDetails = false }) => {
  const { supabaseStatus } = useChat();
  
  if (!supabaseStatus) {
    return null;
  }
  
  const { connected, tablesExist, permissions, details } = supabaseStatus;
  
  const getStatusColor = () => {
    if (connected && permissions?.canRead && permissions?.canWrite && 
        tablesExist?.messages && tablesExist?.chats) {
      return 'text-green-500';
    } else if (connected) {
      return 'text-yellow-500';
    } else {
      return 'text-red-500';
    }
  };
  
  const getStatusIcon = () => {
    if (connected && permissions?.canRead && permissions?.canWrite && 
        tablesExist?.messages && tablesExist?.chats) {
      return <BsCheckCircleFill className={`${getStatusColor()} w-5 h-5`} />;
    } else if (connected) {
      return <BsExclamationTriangleFill className={`${getStatusColor()} w-5 h-5`} />;
    } else {
      return <BsXCircleFill className={`${getStatusColor()} w-5 h-5`} />;
    }
  };
  
  const getStatusText = () => {
    if (connected && permissions?.canRead && permissions?.canWrite && 
        tablesExist?.messages && tablesExist?.chats) {
      return 'Connected';
    } else if (connected) {
      return 'Partially Connected';
    } else {
      return 'Disconnected';
    }
  };
  
  return (
    <div className="bg-white rounded-lg shadow-sm p-3 mb-4">
      <div className="flex items-center">
        {getStatusIcon()}
        <span className={`ml-2 font-medium ${getStatusColor()}`}>
          Supabase: {getStatusText()}
        </span>
      </div>
      
      {showDetails && (
        <div className="mt-2 text-sm text-gray-600">
          <div className="grid grid-cols-2 gap-2">
            <div>Connection:</div>
            <div className={connected ? 'text-green-500' : 'text-red-500'}>
              {connected ? 'Connected' : 'Disconnected'}
            </div>
            
            {connected && (
              <>
                <div>Tables:</div>
                <div>
                  {Object.entries(tablesExist || {}).map(([table, exists]) => (
                    <div key={table} className={exists ? 'text-green-500' : 'text-red-500'}>
                      {table}: {exists ? 'Exists' : 'Missing'}
                    </div>
                  ))}
                </div>
                
                <div>Permissions:</div>
                <div>
                  <div className={permissions?.canRead ? 'text-green-500' : 'text-red-500'}>
                    Read: {permissions?.canRead ? 'Yes' : 'No'}
                  </div>
                  <div className={permissions?.canWrite ? 'text-green-500' : 'text-red-500'}>
                    Write: {permissions?.canWrite ? 'Yes' : 'No'}
                  </div>
                </div>
              </>
            )}
          </div>
          
          {details && (
            <div className="mt-2 p-2 bg-gray-100 rounded text-xs">
              {details}
            </div>
          )}
        </div>
      )}
    </div>
  );
};

export default SupabaseStatus;
