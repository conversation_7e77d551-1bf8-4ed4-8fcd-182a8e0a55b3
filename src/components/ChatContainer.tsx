import React, { useState } from 'react';
import { Chat, Message, User } from '@/types';
import ChatHeader from './ChatHeader';
import Messages from './Messages';
import MessageInput from './MessageInput';
import SupabaseStatus from './SupabaseStatus';
import SupabaseAuthStatus from './SupabaseAuthStatus';
import { useChat } from '@/lib/chat-context';

interface ChatContainerProps {
  selectedChat: Chat | null;
  messages: Message[];
  currentUser: User;
  users: User[];
  onSendMessage: (content: string) => void;
}

const ChatContainer: React.FC<ChatContainerProps> = ({
  selectedChat,
  messages,
  currentUser,
  users,
  onSendMessage
}) => {
  const { supabaseStatus } = useChat();
  const showSupabaseStatus = !supabaseStatus.connected ||
                            !supabaseStatus.tablesExist?.messages ||
                            !supabaseStatus.permissions?.canRead ||
                            !supabaseStatus.permissions?.canWrite;

  if (!selectedChat) {
    return (
      <div className="flex-1 flex flex-col items-center justify-center bg-gray-50">
        {showSupabaseStatus && (
          <div className="w-full max-w-md px-4 mb-4">
            <SupabaseStatus showDetails={true} />
            <SupabaseAuthStatus showDetails={true} />
          </div>
        )}
        <p className="text-gray-500">Select a chat to start messaging</p>
      </div>
    );
  }

  return (
    <div className="flex flex-col flex-1 h-full">
      <ChatHeader chat={selectedChat} />

      {showSupabaseStatus && (
        <div className="px-4 py-2">
          <SupabaseStatus showDetails={false} />
          <SupabaseAuthStatus showDetails={false} />
        </div>
      )}

      <Messages
        messages={messages.filter(m => m.chat_id === selectedChat.id)}
        currentUser={currentUser}
        users={users}
        chatId={selectedChat.id}
      />
      <MessageInput onSendMessage={onSendMessage} chatId={selectedChat.id} />
    </div>
  );
};

export default ChatContainer;
