'use client';

import React, { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import Sidebar from '@/components/Sidebar';
import ChatList from '@/components/ChatList';
import ChatContainer from '@/components/ChatContainer';
import { ChatProvider, useChat } from '@/lib/chat-context';
import { useAuth } from '@/lib/auth-context';
import { BsWhatsapp } from 'react-icons/bs';
import { MdOutlineNoteAlt, MdLogout } from 'react-icons/md';
import { supabase } from '@/lib/supabase';

// Wrapper component that uses the ChatProvider
export default function ChatPage() {
  return (
    <ChatProvider>
      <ProtectedChatPage />
    </ChatProvider>
  );
}

// Protected page that requires authentication
function ProtectedChatPage() {
  const { user, loading } = useAuth();
  const router = useRouter();

  useEffect(() => {
    if (!loading && !user) {
      router.push('/auth/login');
    }
  }, [user, loading, router]);

  // Show loading state while checking authentication
  if (loading) {
    return (
      <div className="flex h-screen items-center justify-center bg-white">
        <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-green-500"></div>
      </div>
    );
  }

  // If authenticated, show chat content
  if (user) {
    return <ChatPageContent />;
  }

  // This will briefly show before redirect happens
  return null;
}

// Content component that uses the chat context
function ChatPageContent() {
  const {
    chats,
    messages,
    selectedChat,
    currentUser,
    setSelectedChat,
    sendMessage
  } = useChat();
  const [loggingOut, setLoggingOut] = useState(false);
  const router = useRouter();

  const handleSendMessage = (content: string) => {
    if (!selectedChat) return;
    sendMessage(content, selectedChat.id);
  };

  const handleLogout = async () => {
    try {
      setLoggingOut(true);
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      router.push('/auth/login');
    } catch (error) {
      console.error('Error logging out:', error);
    } finally {
      setLoggingOut(false);
    }
  };



  return (
    <main className="flex h-screen bg-white">
      <Sidebar />

      <div className="flex flex-1 h-full overflow-hidden">
        <ChatList
          chats={chats}
          selectedChat={selectedChat}
          onSelectChat={setSelectedChat}
        />

        <div className="flex-1 flex flex-col">
          <ChatContainer
            selectedChat={selectedChat}
            messages={messages.filter(m => selectedChat && m && m.chat_id === selectedChat.id)}
            currentUser={currentUser}
            users={Array.isArray(chats)
              ? chats
                  .filter(chat => chat && Array.isArray(chat.participants))
                  .flatMap(chat => chat.participants)
                  .filter(user => user && user.id) // Filter out any undefined or invalid users
              : []
            }
            onSendMessage={handleSendMessage}
          />
        </div>

        {/* Right sidebar */}
        <div className="w-14 border-l border-gray-200 flex flex-col bg-white">
          {/* Top buttons */}
          <div className="flex flex-col items-center py-4 gap-5">
            <button className="text-gray-500 hover:text-gray-700">
              <span className="inline-flex items-center justify-center w-8 h-8 bg-gray-100 rounded-md">
                <BsWhatsapp className="w-4 h-4 text-gray-600" />
              </span>
            </button>
            <button className="text-gray-500 hover:text-gray-700">
              <MdOutlineNoteAlt className="w-5 h-5" />
            </button>
          </div>

          {/* Bottom indicators */}
          <div className="mt-auto flex flex-col items-center py-6 gap-5">
            <button className="flex flex-col items-center justify-center" title="Help">
              <span className="inline-flex items-center justify-center w-8 h-8 rounded-md">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M12 21C16.9706 21 21 16.9706 21 12C21 7.02944 16.9706 3 12 3C7.02944 3 3 7.02944 3 12C3 16.9706 7.02944 21 12 21Z" stroke="#6B7280" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 8V12" stroke="#6B7280" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 16H12.01" stroke="#6B7280" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </span>
            </button>
            <button className="flex flex-col items-center justify-center" title="Mobile">
              <span className="inline-flex items-center justify-center w-8 h-8 rounded-md">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M16 4H8C7.44772 4 7 4.44772 7 5V19C7 19.5523 7.44772 20 8 20H16C16.5523 20 17 19.5523 17 19V5C17 4.44772 16.5523 4 16 4Z" stroke="#6B7280" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M12 17H12.01" stroke="#6B7280" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </span>
            </button>
            <button className="flex flex-col items-center justify-center" title="Settings">
              <span className="inline-flex items-center justify-center w-8 h-8 rounded-md">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                  <path d="M3 9H21" stroke="#6B7280" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M9 21V9" stroke="#6B7280" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                  <path d="M3 5V19C3 20.1046 3.89543 21 5 21H19C20.1046 21 21 20.1046 21 19V5C21 3.89543 20.1046 3 19 3H5C3.89543 3 3 3.89543 3 5Z" stroke="#6B7280" strokeWidth="2" strokeLinecap="round" strokeLinejoin="round"/>
                </svg>
              </span>
            </button>
            <button
              onClick={handleLogout}
              disabled={loggingOut}
              className="flex flex-col items-center justify-center mt-4 text-red-500 hover:text-red-700 transition-colors"
              title="Logout"
            >
              <span className="inline-flex items-center justify-center w-8 h-8 rounded-md">
                <MdLogout className="w-5 h-5" />
              </span>
            </button>
          </div>
        </div>
      </div>
    </main>
  );
}
