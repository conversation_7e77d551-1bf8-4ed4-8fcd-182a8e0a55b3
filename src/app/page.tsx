'use client';

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';

export default function Home() {
  const router = useRouter();
  
  useEffect(() => {
    // Always redirect directly to chat page
    router.push('/chat');
  }, [router]);

  return (
    <main className="flex min-h-screen flex-col items-center justify-center p-24">
      <div className="w-16 h-16 rounded-md bg-green-500 flex items-center justify-center text-white mb-4">
        <span className="font-bold text-2xl">P</span>
      </div>
      <p className="text-gray-500">Loading Periskope chat application...</p>
    </main>
  );
}
