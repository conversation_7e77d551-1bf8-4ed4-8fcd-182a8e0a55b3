'use client';

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { <PERSON>Phone, <PERSON>Lock, FiAlertCircle, FiUser, FiMail } from 'react-icons/fi';
import { supabase } from '@/lib/supabase';

export default function LoginPage() {
  const [isLogin, setIsLogin] = useState(true);
  const [mobile, setMobile] = useState('');
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [fullName, setFullName] = useState('');
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setError(null);
    setLoading(true);

    try {
      if (isLogin) {
        // Login with email and password using Supabase directly
        const { data, error } = await supabase.auth.signInWithPassword({
          email: email,
          password: password
        });
        
        if (error) {
          setError('Invalid email or password');
        } else {
          router.push('/chat');
        }
      } else {
        // Register with email, mobile, and full name
        if (!fullName.trim()) {
          setError('Full name is required');
          setLoading(false);
          return;
        }
        
        if (!email.trim()) {
          setError('Email is required');
          setLoading(false);
          return;
        }
        
        // Validate email format
        const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
        if (!emailRegex.test(email)) {
          setError('Please enter a valid email address');
          setLoading(false);
          return;
        }

        if (!mobile.trim()) {
          setError('Phone number is required');
          setLoading(false);
          return;
        }

        let userData = null;
        
        try {
          // Sign up with email and password using Supabase directly
          const { data, error } = await supabase.auth.signUp({
            email: email.trim(),
            password: password,
            options: {
              data: {
                full_name: fullName,
                phone: mobile
              },
            },
          });
          
          if (error) {
            console.error('Signup error:', error);
            if (error.message.includes('email') || error.code === 'email_address_invalid') {
              setError('Invalid email format. Please check your email address.');
            } else if (error.message.includes('password')) {
              setError('Password must be at least 6 characters.');
            } else {
              setError(`Error creating account: ${error.message}`);
            }
            setLoading(false);
            return;
          }
          
          if (!data.user) {
            setError('Failed to create user. Please try again.');
            setLoading(false);
            return;
          }
          
          userData = data.user;
          
        } catch (err) {
          console.error('Unexpected error during signup:', err);
          setError('An unexpected error occurred. Please try again.');
          setLoading(false);
          return;
        }
        
        // If we get here, the signup was successful
        try {
          // Create user profile in the profiles table
          if (userData) {
            console.log('Creating profile for user:', userData.id);
            
            // Prepare profile data
            const profileData = {
              id: userData.id,
              full_name: fullName,
              email: email,
              phone: mobile,
              created_at: new Date().toISOString(),
              updated_at: new Date().toISOString(),
              avatar_url: null,  // Can be updated later
          
            };
            
            // Insert into profiles table
            const { data: profile, error: profileError } = await supabase
              .from('profiles')
              .insert([profileData])
              .select();

            if (profileError) {
              console.error('Profile creation error:', profileError);
              setError(`Error creating profile: ${profileError.message}`);
              setLoading(false);
              return;
            }
            
            console.log('Profile created successfully:', profile);
          } else {
            console.error('Cannot create profile: No user data available');
            setError('Error creating user profile: Missing user data');
            setLoading(false);
            return;
          }
          
          // All done, redirect to chat
          router.push('/chat');
        } catch (profileErr) {
          console.error('Error creating profile:', profileErr);
          setError('Error creating user profile. Please try again.');
          setLoading(false);
          return;
        }
      }
    } catch (err) {
      setError('An error occurred. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="flex min-h-screen bg-gray-50">
      <div className="w-14 bg-white flex flex-col items-center py-4 border-r border-gray-200">
        <div className="mb-6">
          <div className="w-8 h-8 rounded-md bg-green-500 flex items-center justify-center text-white">
            <span className="font-bold text-lg">P</span>
          </div>
        </div>
      </div>
      
      <div className="flex-1 flex items-center justify-center">
        <div className="w-full max-w-md p-8 bg-white rounded-lg shadow-md">
          <div className="text-center mb-8">
            <h1 className="text-2xl font-bold text-gray-800">Welcome to Periskope</h1>
            <p className="text-gray-600 mt-2">
              {isLogin 
                ? 'Sign in to continue to your account' 
                : 'Create a new account to get started'}
            </p>
          </div>
          
          {error && (
            <div className="mb-4 p-3 bg-red-50 text-red-700 rounded-md flex items-center">
              <FiAlertCircle className="mr-2" />
              <span>{error}</span>
            </div>
          )}
          
          <form onSubmit={handleSubmit}>
            {!isLogin && (
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="fullName">
                  Full Name
                </label>
                <div className="relative">
                  <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                    <FiUser />
                  </span>
                  <input
                    id="fullName"
                    type="text"
                    value={fullName}
                    onChange={(e) => setFullName(e.target.value)}
                    className="w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="Enter your full name"
                    required={!isLogin}
                  />
                </div>
              </div>
            )}
            
            <div className="mb-4">
              <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="email">
                Email Address
              </label>
              <div className="relative">
                <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                  <FiMail />
                </span>
                <input
                  id="email"
                  type="email"
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Enter your email address"
                  required
                />
              </div>
            </div>
            
            {!isLogin ? (
              <div className="mb-4">
                <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="mobile">
                  Mobile Number
                </label>
                <div className="relative">
                  <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                    <FiPhone />
                  </span>
                  <input
                    id="mobile"
                    type="tel"
                    value={mobile}
                    onChange={(e) => setMobile(e.target.value)}
                    className="w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                    placeholder="Enter your mobile number"
                    required={!isLogin}
                  />
                </div>
              </div>
            ) : null}
            
            <div className="mb-6">
              <label className="block text-gray-700 text-sm font-medium mb-2" htmlFor="password">
                Password
              </label>
              <div className="relative">
                <span className="absolute inset-y-0 left-0 flex items-center pl-3 text-gray-500">
                  <FiLock />
                </span>
                <input
                  id="password"
                  type="password"
                  value={password}
                  onChange={(e) => setPassword(e.target.value)}
                  className="w-full pl-10 pr-3 py-2 border border-gray-200 rounded-md focus:outline-none focus:ring-2 focus:ring-green-500 focus:border-transparent"
                  placeholder="Enter your password"
                  required
                />
              </div>
            </div>
            
            <button
              type="submit"
              disabled={loading}
              className="w-full bg-green-500 text-white py-2 rounded-md hover:bg-green-600 focus:outline-none focus:ring-2 focus:ring-green-500 focus:ring-opacity-50 transition-colors disabled:opacity-70"
            >
              {loading 
                ? (isLogin ? 'Signing in...' : 'Creating account...') 
                : (isLogin ? 'Sign In' : 'Create Account')}
            </button>
            
            <div className="mt-4 text-center">
              <button
                type="button"
                onClick={() => setIsLogin(!isLogin)}
                className="text-green-600 text-sm hover:underline focus:outline-none"
              >
                {isLogin 
                  ? "Don't have an account? Sign up" 
                  : "Already have an account? Sign in"}
              </button>
            </div>
          </form>
        </div>
      </div>
    </div>
  );
}
