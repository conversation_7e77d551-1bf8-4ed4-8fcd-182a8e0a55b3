import { createClient } from '@supabase/supabase-js';
import { Database } from '@/types/supabase';
import { SUPABASE_URL, SUPABASE_ANON_KEY } from './supabase-config';

// Validate Supabase configuration before creating client
if (!SUPABASE_URL || !SUPABASE_URL.startsWith('http')) {
  console.error('Invalid Supabase URL:', SUPABASE_URL);
}

if (!SUPABASE_ANON_KEY) {
  console.error('Missing Supabase anon key');
}

// Create Supabase client
let supabaseClient;

try {
  console.log('Creating Supabase client with URL:', SUPABASE_URL);

  supabaseClient = createClient<Database>(
    SUPABASE_URL,
    SUPABASE_ANON_KEY,
    {
      auth: {
        persistSession: true,
        autoRefreshToken: true,
        detectSessionInUrl: true
      }
    }
  );

  // Test the connection
  supabaseClient.auth.getSession()
    .then(response => {
      if (response.error) {
        console.error('Supabase connection test failed:', response.error.message);
      } else {
        console.log('Supabase connection test successful');
      }
    })
    .catch(error => {
      console.error('Supabase connection test error:', error);
    });

} catch (error) {
  console.error('Error creating Supabase client:', error);

  // Create a dummy client that logs errors instead of crashing
  supabaseClient = {
    from: () => ({
      select: () => ({
        eq: () => ({
          order: () => ({
            then: () => Promise.resolve({ data: [], error: new Error('Supabase client initialization failed') })
          }),
          then: () => Promise.resolve({ data: [], error: new Error('Supabase client initialization failed') })
        }),
        then: () => Promise.resolve({ data: [], error: new Error('Supabase client initialization failed') })
      }),
      insert: () => ({
        select: () => ({
          then: () => Promise.resolve({ data: null, error: new Error('Supabase client initialization failed') })
        }),
        then: () => Promise.resolve({ data: null, error: new Error('Supabase client initialization failed') })
      }),
      update: () => ({
        eq: () => ({
          then: () => Promise.resolve({ data: null, error: new Error('Supabase client initialization failed') })
        }),
        then: () => Promise.resolve({ data: null, error: new Error('Supabase client initialization failed') })
      }),
      delete: () => ({
        eq: () => ({
          then: () => Promise.resolve({ data: null, error: new Error('Supabase client initialization failed') })
        }),
        then: () => Promise.resolve({ data: null, error: new Error('Supabase client initialization failed') })
      })
    }),
    auth: {
      getSession: () => Promise.resolve({ data: { session: null }, error: new Error('Supabase client initialization failed') }),
      signIn: () => Promise.resolve({ data: null, error: new Error('Supabase client initialization failed') }),
      signOut: () => Promise.resolve({ error: new Error('Supabase client initialization failed') })
    },
    channel: () => ({
      on: () => ({ subscribe: () => ({ unsubscribe: () => {} }) }),
      subscribe: () => ({ unsubscribe: () => {} })
    })
  };
}

// Export the client
export const supabase = supabaseClient;
