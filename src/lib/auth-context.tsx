'use client';

import React, { createContext, useState, useContext, useEffect, ReactNode } from 'react';
import { User as SupabaseUser } from '@supabase/supabase-js';
import { User } from '@/types';
import { supabase } from './supabase';

interface AuthContextType {
  user: User | null;
  supabaseUser: SupabaseUser | null;
  loading: boolean;
  signUpWithMobile: (mobile: string, email: string, password: string, fullName: string) => Promise<{ error: any }>;
  signInWithMobile: (mobile: string, password: string) => Promise<{ error: any }>;
  logout: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType>({
  user: null,
  supabaseUser: null,
  loading: true,
  signUpWithMobile: async () => ({ error: null }),
  signInWithMobile: async () => ({ error: null }),
  logout: async () => {},
});

export const AuthProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [supabaseUser, setSupabaseUser] = useState<SupabaseUser | null>(null);
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);

  // Fetch user profile from Supabase
  const fetchUserProfile = async (userId: string) => {
    try {
      const { data, error } = await supabase
        .from('profiles')
        .select('*')
        .eq('id', userId)
        .single();

      if (error) throw error;

      if (data) {
        setUser({
          id: data.id,
          name: data.full_name || 'User',
          email: data.mobile, // Using mobile as identifier
          status: 'online',
          avatar: data.avatar_url,
        });
      }
    } catch (error) {
      console.error('Error fetching user profile:', error);
    }
  };

  // Check for existing session and update user state
  useEffect(() => {
    const checkSession = async () => {
      setLoading(true);
      
      // Get current session
      const { data: { session } } = await supabase.auth.getSession();
      
      if (session?.user) {
        setSupabaseUser(session.user);
        await fetchUserProfile(session.user.id);
      }
      
      setLoading(false);
      
      // Listen for auth changes
      const { data: { subscription } } = supabase.auth.onAuthStateChange(
        async (event, session) => {
          if (session?.user) {
            setSupabaseUser(session.user);
            await fetchUserProfile(session.user.id);
          } else {
            setSupabaseUser(null);
            setUser(null);
          }
        }
      );
      
      return () => {
        subscription.unsubscribe();
      };
    };

    checkSession();
  }, []);

  // Sign up with mobile and password
  const signUpWithMobile = async (mobile: string, password: string, fullName: string) => {
    try {
      // Create a unique email using mobile number
      const email = `${mobile.replace(/\+/g, '').replace(/\s/g, '')}@periskope.mobile`;
      
      // Sign up with email but store mobile number
      const { data, error } = await supabase.auth.signUp({
        email: email,
        password: password,
        options: {
          data: {
            mobile: mobile,
            full_name: fullName,
          },
        },
      });

      if (error) throw error;

      // Create user profile
      if (data.user) {
        // Using auth.users.id as the primary key for profiles
        const { error: profileError } = await supabase.from('profiles').insert([
          {
            id: data.user.id, // Explicitly using auth.users.id as profiles.id
            mobile: mobile,
            full_name: fullName,
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString(),
          },
        ]);

        if (profileError) throw profileError;
        
        // Fetch the created profile to update local state
        await fetchUserProfile(data.user.id);
      }

      return { error: null };
    } catch (error) {
      console.error('Error signing up:', error);
      return { error };
    }
  };

  // Sign in with mobile and password
  const signInWithMobile = async (mobile: string, password: string) => {
    try {
      // Convert mobile to email format
      const email = `${mobile.replace(/\+/g, '').replace(/\s/g, '')}@periskope.mobile`;
      
      // Sign in with email
      const { data, error } = await supabase.auth.signInWithPassword({
        email: email,
        password: password,
      });

      if (error) throw error;

      return { error: null };
    } catch (error) {
      console.error('Error signing in:', error);
      return { error };
    }
  };

  // Logout
  const logout = async () => {
    try {
      const { error } = await supabase.auth.signOut();
      if (error) throw error;
      
      setSupabaseUser(null);
      setUser(null);
    } catch (error) {
      console.error('Error logging out:', error);
    }
  };

  return (
    <AuthContext.Provider value={{ 
      user, 
      supabaseUser,
      loading, 
      signUpWithMobile, 
      signInWithMobile, 
      logout 
    }}>
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = () => useContext(AuthContext);
