'use client';

import React, { createContext, useState, useContext, useEffect, ReactNode, useRef } from 'react';
import { Chat, Message, User } from '@/types';
import { mockChats, mockMessages, mockUsers } from '@/utils/mock-data';
import * as idbService from '@/utils/idb-service';
import { realtimeService } from './realtime-service';
import { supabase } from './supabase';
import { useAuth } from './auth-context';
import { checkSupabaseConnection, checkUserPermissions } from '@/utils/supabase-checker';
import { logEnvStatus, checkSupabaseEnv } from '@/utils/env-checker';

interface ChatContextType {
  chats: Chat[];
  messages: Message[];
  selectedChat: Chat | null;
  currentUser: User;
  onlineUsers: Set<string>;
  typingUsers: Map<string, string>; // Map of userId to chatId
  supabaseStatus: {
    connected: boolean;
    tablesExist: Record<string, boolean>;
    permissions?: { canRead: boolean; canWrite: boolean };
    error?: any;
    details?: string;
    auth?: { isAuthenticated: boolean; user?: any };
  };
  setSelectedChat: (chat: Chat) => void;
  addChat: (chat: Chat) => void; // New function to add a chat
  sendMessage: (content: string, chatId: string) => void;
  loadMessagesForChat: (chatId: string) => Promise<void>;
  setTyping: (chatId: string, isTyping: boolean) => void;
}

const ChatContext = createContext<ChatContextType>({
  chats: [],
  messages: [],
  selectedChat: null,
  currentUser: mockUsers[0],
  onlineUsers: new Set<string>(),
  typingUsers: new Map<string, string>(),
  supabaseStatus: {
    connected: false,
    tablesExist: {},
  },
  setSelectedChat: () => {},
  addChat: () => {}, // New function to add a chat
  sendMessage: () => {},
  loadMessagesForChat: async () => {},
  setTyping: () => {},
});

export const ChatProvider: React.FC<{ children: ReactNode }> = ({ children }) => {
  const [chats, setChats] = useState<Chat[]>([]);
  const [messages, setMessages] = useState<Message[]>([]);
  const [selectedChat, setSelectedChat] = useState<Chat | null>(null);
  const [loading, setLoading] = useState(true);
  const [onlineUsers, setOnlineUsers] = useState<Set<string>>(new Set());
  const [typingUsers, setTypingUsers] = useState<Map<string, string>>(new Map());
  const [currentUser, setCurrentUser] = useState<User>(mockUsers.find(u => u.id === '4') || mockUsers[0]);
  const [initialized, setInitialized] = useState(false);
  const [supabaseStatus, setSupabaseStatus] = useState<{
    connected: boolean;
    tablesExist: Record<string, boolean>;
    permissions?: { canRead: boolean; canWrite: boolean };
    error?: any;
    details?: string;
    auth?: { isAuthenticated: boolean; user?: any };
  }>({ connected: false, tablesExist: {} });
  const { user: authUser } = useAuth();
  const realtimeInitialized = useRef(false);

  // Check Supabase connection
  useEffect(() => {
    const checkConnection = async () => {
      try {
        console.log('Checking Supabase connection...');

        // Log environment variables status
        logEnvStatus();

        // Check if Supabase environment variables are properly set
        const envCheck = checkSupabaseEnv();

        console.log('Supabase environment check:', envCheck);

        if (!envCheck.isValid) {
          const error = new Error('Invalid Supabase configuration');
          console.error('Supabase configuration error:', error);
          setSupabaseStatus({
            connected: false,
            tablesExist: {},
            permissions: { canRead: false, canWrite: false },
            error,
            details: `Invalid Supabase configuration: ${envCheck.errors.join(', ')}`
          });
          return;
        }

        // Check if Supabase client is properly initialized
        const supabaseUrl = supabase.supabaseUrl;
        const hasAnon = !!supabase.supabaseKey;

        console.log('Supabase client configuration:', {
          url: supabaseUrl,
          hasAnonKey: hasAnon,
          urlValid: supabaseUrl && supabaseUrl.startsWith('http')
        });

        if (!supabaseUrl || !supabaseUrl.startsWith('http') || !hasAnon) {
          const error = new Error('Invalid Supabase client configuration');
          console.error('Supabase client configuration error:', error);
          setSupabaseStatus({
            connected: false,
            tablesExist: {},
            permissions: { canRead: false, canWrite: false },
            error,
            details: 'Invalid Supabase client configuration. The client was not properly initialized.'
          });
          return;
        }

        // Check connection
        const connectionStatus = await checkSupabaseConnection();
        console.log('Supabase connection status:', connectionStatus);

        if (connectionStatus.connected) {
          const permissionsStatus = await checkUserPermissions();
          console.log('Supabase permissions status:', permissionsStatus);

          setSupabaseStatus({
            ...connectionStatus,
            permissions: permissionsStatus,
            auth: permissionsStatus.auth
          });

          // Log detailed status for debugging
          console.log('Final Supabase status:', {
            connected: connectionStatus.connected,
            tablesExist: connectionStatus.tablesExist,
            permissions: permissionsStatus,
            details: connectionStatus.details,
            auth: permissionsStatus.auth
          });
        } else {
          setSupabaseStatus(connectionStatus);
          console.log('Supabase not connected:', connectionStatus.details || 'Unknown error');
        }
      } catch (error) {
        console.error('Error checking Supabase connection:', error);
        setSupabaseStatus({
          connected: false,
          tablesExist: {},
          permissions: { canRead: false, canWrite: false },
          error,
          details: `Unexpected error checking Supabase connection: ${error?.message || 'Unknown error'}`
        });
      }
    };

    checkConnection();
  }, []);

  // Initialize data from mock data and localStorage
  useEffect(() => {
    const initData = async () => {
      try {
        // Initialize with mock data
        await idbService.initializeWithMockData(mockChats, mockMessages);

        // Load chats from IndexedDB for offline support
        const storedChats = await idbService.getAllChats();
        setChats(storedChats);

        // Set first chat as selected
        if (storedChats.length > 0) {
          const firstChat = storedChats[0];
          setSelectedChat(firstChat);
          await loadMessagesForChat(firstChat.id);
        }

        setInitialized(true);
      } catch (error) {
        console.error('Error initializing data:', error);
      }
    };

    if (!initialized) {
      initData();
    }
  }, [initialized]);

  // Update current user when auth user changes
  useEffect(() => {
    if (authUser) {
      setCurrentUser({
        id: authUser.id,
        name: authUser.name,
        email: authUser.email,
        status: 'online',
        avatar: authUser.avatar
      });
    }
  }, [authUser]);

  // Initialize real-time service
  useEffect(() => {
    if (!currentUser?.id || realtimeInitialized.current) return;

    // Initialize the real-time service with the current user
    realtimeService.initialize(currentUser.id);

    // Set up message handler
    realtimeService.onMessage((newMessage) => {
      // Only add the message if it's not from the current user
      if (newMessage.sender_id !== currentUser.id) {
        setMessages(prev => {
          // Check if message already exists
          if (prev.some(m => m.id === newMessage.id)) {
            return prev.map(m => m.id === newMessage.id ? newMessage : m);
          }
          return [...prev, newMessage];
        });

        // Update chat's last message if it's the selected chat
        if (selectedChat && newMessage.chat_id === selectedChat.id) {
          setChats(prev =>
            prev.map(c => {
              if (c.id === newMessage.chat_id) {
                return { ...c, last_message: newMessage, updated_at: newMessage.timestamp || newMessage.created_at || new Date().toISOString() };
              }
              return c;
            }).sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
          );

          // Mark message as read if it's in the current chat
          realtimeService.markMessageAsRead(newMessage.id);
        }
      }
    });

    // Set up user status handler
    realtimeService.onUserStatus((user, status) => {
      if (status === 'online') {
        setOnlineUsers(prev => new Set(prev).add(user.id));
      } else {
        setOnlineUsers(prev => {
          const newSet = new Set(prev);
          newSet.delete(user.id);
          return newSet;
        });
      }
    });

    // Set up typing indicator handler
    realtimeService.onTyping((userId, chatId, isTyping) => {
      setTypingUsers(prev => {
        const newMap = new Map(prev);
        if (isTyping) {
          newMap.set(userId, chatId);
        } else {
          if (prev.get(userId) === chatId) {
            newMap.delete(userId);
          }
        }
        return newMap;
      });
    });

    // Set up error handler
    realtimeService.onError((error) => {
      console.error('Real-time service error:', error);
    });

    realtimeInitialized.current = true;

    // Clean up on unmount
    return () => {
      realtimeService.cleanup();
      realtimeInitialized.current = false;
    };
  }, [currentUser?.id, selectedChat]);

  const loadMessagesForChat = async (chatId: string) => {
    try {
      console.log(`Loading messages for chat ${chatId}...`);

      // Subscribe to real-time updates for this chat
      realtimeService.subscribeToChat(chatId);

      let supabaseMessages: any[] = [];

      // First check if Supabase is connected
      if (supabaseStatus.connected &&
          supabaseStatus.tablesExist?.messages &&
          supabaseStatus.permissions?.canRead) {
        try {
          console.log('Fetching messages from Supabase for chat:', chatId);
          const { data, error } = await supabase
            .from('messages')
            .select('*')
            .eq('chat_id', chatId)
            .order('created_at', { ascending: true })
            .catch(e => {
              console.error('Caught error in Supabase query:', e);
              return { data: null, error: e };
            });

          if (error) {
            console.error('Error loading messages from Supabase:', error);
            console.error('Error details:', error.message, error.code, error.details);
            throw error;
          }

          if (data) {
            console.log(`Retrieved ${data.length} messages from Supabase`);
            supabaseMessages = data;
          }
        } catch (supabaseError) {
          console.error('Supabase query error:', supabaseError);
          // Continue with local data if Supabase fails
        }
      } else {
        console.log('Skipping Supabase query because:', {
          connected: supabaseStatus.connected,
          messagesTableExists: supabaseStatus.tablesExist?.messages,
          canRead: supabaseStatus.permissions?.canRead,
          details: supabaseStatus.details
        });
      }

      // Load messages from localStorage for offline support
      const localStorageKey = `messages_${chatId}`;
      const storedMessages = JSON.parse(localStorage.getItem(localStorageKey) || '[]');
      console.log(`Retrieved ${storedMessages.length} messages from localStorage`);

      // Also load from IndexedDB for offline support
      const idbMessages = await idbService.getMessagesByChat(chatId);
      console.log(`Retrieved ${idbMessages.length} messages from IndexedDB`);

      // Combine messages from all sources, handling empty arrays safely
      setMessages(prevMessages => {
        // Create a map to deduplicate messages
        const messageMap = new Map();

        // Add previous messages
        if (prevMessages && prevMessages.length > 0) {
          prevMessages.forEach(msg => {
            if (msg && msg.id) messageMap.set(msg.id, msg);
          });
        }

        // Add Supabase messages
        if (supabaseMessages && supabaseMessages.length > 0) {
          supabaseMessages.forEach((msg: any) => {
            if (msg && msg.id) {
              // Convert Supabase message format to our Message type
              const formattedMsg: Message = {
                id: msg.id,
                content: msg.content,
                timestamp: msg.created_at,
                sender_id: msg.user_id, // Map user_id from Supabase to sender_id in our app
                chat_id: msg.chat_id,
                read: msg.read || false,
                created_at: msg.created_at
              };
              messageMap.set(msg.id, formattedMsg);
            }
          });
        }

        // Add IndexedDB messages
        if (idbMessages && idbMessages.length > 0) {
          idbMessages.forEach(msg => {
            if (msg && msg.id) messageMap.set(msg.id, msg);
          });
        }

        // Add localStorage messages
        if (storedMessages && storedMessages.length > 0) {
          storedMessages.forEach((msg: Message) => {
            if (msg && msg.id) messageMap.set(msg.id, msg);
          });
        }

        // Convert map values to array and sort by timestamp
        const sortedMessages = Array.from(messageMap.values())
          .sort((a, b) => {
            const timeA = a.timestamp || a.created_at || '';
            const timeB = b.timestamp || b.created_at || '';
            return new Date(timeA).getTime() - new Date(timeB).getTime();
          });

        console.log(`Total combined messages: ${sortedMessages.length}`);
        return sortedMessages;
      });

      // Mark all messages as read
      if (supabaseMessages && supabaseMessages.length > 0) {
        supabaseMessages.forEach((msg: any) => {
          if (msg && !msg.read && msg.user_id !== currentUser.id) {
            realtimeService.markMessageAsRead(msg.id);
          }
        });
      }
    } catch (error) {
      console.error('Error loading messages:', error);
      // Ensure we set an empty array if all else fails
      setMessages([]);
    }
  };

  const sendMessage = async (content: string, chatId: string) => {
    if (!content.trim()) return;

    try {
      const now = new Date().toISOString();

      // Generate a UUID for the message
      const messageId = crypto.randomUUID();

      const newMessage: Message = {
        id: messageId,
        content,
        timestamp: now,
        sender_id: currentUser.id,
        chat_id: chatId,
        read: false,
        created_at: now
      };

      // First check if Supabase is connected
      let sentMessage = null;
      if (supabaseStatus.connected &&
          supabaseStatus.tablesExist?.messages &&
          supabaseStatus.permissions?.canWrite) {
        try {
          console.log('Sending message to Supabase:', newMessage);
          sentMessage = await realtimeService.sendMessage(newMessage);
          console.log('Message sent successfully:', sentMessage);
        } catch (realtimeError) {
          console.error('Error sending message via real-time service:', realtimeError);
        }
      } else {
        console.log('Skipping Supabase send because:', {
          connected: supabaseStatus.connected,
          messagesTableExists: supabaseStatus.tablesExist?.messages,
          canWrite: supabaseStatus.permissions?.canWrite,
          details: supabaseStatus.details
        });
      }

      // If real-time service fails or is not available, save locally
      if (!sentMessage) {
        // Save to localStorage
        try {
          const localStorageKey = `messages_${chatId}`;
          const storedMessages = JSON.parse(localStorage.getItem(localStorageKey) || '[]');
          storedMessages.push(newMessage);
          localStorage.setItem(localStorageKey, JSON.stringify(storedMessages));
        } catch (storageError) {
          console.error('Error storing message in localStorage:', storageError);
        }

        // Save to IndexedDB for offline access
        await idbService.addMessage(newMessage);
      }

      // Update messages state with the message we just sent
      setMessages(prev => [...prev, newMessage]);

      // Find the chat to update
      const existingChat = chats.find(c => c && c.id === chatId);

      // Only proceed if we found a valid chat
      if (existingChat && existingChat.id) {
        console.log('Updating chat:', existingChat.id);

        // Create a copy of the existing chat
        const updatedChat = {...existingChat};

        // Update the chat properties
        updatedChat.last_message = newMessage;
        updatedChat.updated_at = now;

        // Log the chat before saving to help with debugging
        console.log('Chat to be saved:', {
          id: updatedChat.id,
          name: updatedChat.name,
          updated_at: updatedChat.updated_at,
          has_last_message: !!updatedChat.last_message
        });

        try {
          // Update in IndexedDB
          await idbService.addChat(updatedChat);
          console.log('Chat successfully saved to IndexedDB');

          // Update chats state and sort by updated_at
          setChats(prev =>
            prev.map(c => c && c.id === chatId ? updatedChat : c)
               .sort((a, b) => new Date(b.updated_at).getTime() - new Date(a.updated_at).getTime())
          );
        } catch (dbError) {
          console.error('Error saving chat to IndexedDB:', dbError);
          console.error('Chat object that caused the error:', updatedChat);
        }
      } else {
        console.error('Could not find chat with ID:', chatId);
        console.log('Available chats:', chats.map(c => ({ id: c.id, name: c.name })));
      }
    } catch (error) {
      console.error('Error in sendMessage function:', error);
    }
  };

  // Function to send typing indicator
  const setTyping = (chatId: string, isTyping: boolean) => {
    if (!chatId) return;
    realtimeService.sendTypingIndicator(chatId, isTyping);
  };

  // Function to add a new chat to the global state
  const addChat = (chat: Chat) => {
    console.log('Adding chat to global state:', chat);

    // Check if the chat already exists
    const chatExists = chats.some(c => c && c.id === chat.id);

    if (!chatExists) {
      // Add the chat to the global state
      setChats(prevChats => {
        const updatedChats = [chat, ...prevChats];
        console.log('Updated chats state:', updatedChats.map(c => ({ id: c.id, name: c.name })));
        return updatedChats;
      });
    } else {
      console.log('Chat already exists in global state:', chat.id);
    }
  };

  return (
    <ChatContext.Provider value={{
      chats,
      messages,
      selectedChat,
      currentUser,
      onlineUsers,
      typingUsers,
      supabaseStatus,
      setSelectedChat: (chat: Chat) => {
        setSelectedChat(chat);
        loadMessagesForChat(chat.id);
      },
      addChat, // Add the new function
      sendMessage,
      loadMessagesForChat,
      setTyping
    }}>
      {children}
    </ChatContext.Provider>
  );
};

export const useChat = () => useContext(ChatContext);
