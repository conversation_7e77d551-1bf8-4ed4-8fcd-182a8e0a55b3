import { RealtimeChannel, RealtimePresenceState } from '@supabase/supabase-js';
import { supabase } from './supabase';
import { Message, User } from '@/types';

// Types for realtime events
type MessageHandler = (message: Message) => void;
type UserStatusHandler = (user: User, status: 'online' | 'offline') => void;
type TypingHandler = (userId: string, chatId: string, isTyping: boolean) => void;
type ErrorHandler = (error: Error) => void;

// Realtime service for handling Supabase Realtime subscriptions
class RealtimeService {
  private messageChannel: RealtimeChannel | null = null;
  private presenceChannel: RealtimeChannel | null = null;
  private typingChannel: RealtimeChannel | null = null;

  private messageHandlers: MessageHandler[] = [];
  private userStatusHandlers: UserStatusHandler[] = [];
  private typingHandlers: TypingHandler[] = [];
  private errorHandlers: <PERSON>rror<PERSON>andler[] = [];

  private currentUserId: string | null = null;
  private activeChats: Set<string> = new Set();

  // Initialize the service with the current user
  initialize(userId: string): void {
    this.currentUserId = userId;
    this.setupPresenceChannel();
  }

  // Set up presence channel for online status
  private setupPresenceChannel(): void {
    if (!this.currentUserId) return;

    this.presenceChannel = supabase.channel('online-users', {
      config: {
        presence: {
          key: this.currentUserId,
        },
      },
    });

    this.presenceChannel
      .on('presence', { event: 'join' }, ({ key, newPresences }) => {
        // Handle user coming online
        newPresences.forEach((presence: any) => {
          const userId = presence.user_id;
          if (userId && userId !== this.currentUserId) {
            this.notifyUserStatus(userId, 'online');
          }
        });
      })
      .on('presence', { event: 'leave' }, ({ key, leftPresences }) => {
        // Handle user going offline
        leftPresences.forEach((presence: any) => {
          const userId = presence.user_id;
          if (userId && userId !== this.currentUserId) {
            this.notifyUserStatus(userId, 'offline');
          }
        });
      })
      .subscribe(async (status) => {
        if (status === 'SUBSCRIBED') {
          // Track the current user's presence
          await this.presenceChannel?.track({
            user_id: this.currentUserId,
            online_at: new Date().toISOString(),
          });
        }
      });
  }

  // Subscribe to messages for a specific chat
  subscribeToChat(chatId: string): void {
    try {
      console.log(`Subscribing to chat ${chatId}...`);

      if (!this.messageChannel) {
        this.messageChannel = supabase.channel('messages');

        console.log('Creating new message channel...');

        this.messageChannel
          .on('postgres_changes',
            {
              event: 'INSERT',
              schema: 'public',
              table: 'messages',
              filter: `chat_id=eq.${chatId}`
            },
            (payload) => {
              console.log('Received new message:', payload);
              const newMessage = payload.new as Message;
              this.notifyNewMessage(newMessage);
            }
          )
          .on('postgres_changes',
            {
              event: 'UPDATE',
              schema: 'public',
              table: 'messages',
              filter: `chat_id=eq.${chatId}`
            },
            (payload) => {
              console.log('Received updated message:', payload);
              const updatedMessage = payload.new as Message;
              this.notifyNewMessage(updatedMessage);
            }
          )
          .subscribe((status) => {
            console.log(`Subscription status for chat ${chatId}:`, status);
            if (status === 'SUBSCRIBED') {
              this.activeChats.add(chatId);
              console.log(`Successfully subscribed to chat ${chatId}`);
            } else if (status === 'CHANNEL_ERROR') {
              console.error(`Error subscribing to chat ${chatId}`);
            }
          });
      } else if (!this.activeChats.has(chatId)) {
        console.log(`Adding chat ${chatId} to existing message channel...`);

        // Add filter for the new chat
        this.messageChannel.on('postgres_changes',
          {
            event: 'INSERT',
            schema: 'public',
            table: 'messages',
            filter: `chat_id=eq.${chatId}`
          },
          (payload) => {
            console.log('Received new message:', payload);
            const newMessage = payload.new as Message;
            this.notifyNewMessage(newMessage);
          }
        )
        .on('postgres_changes',
          {
            event: 'UPDATE',
            schema: 'public',
            table: 'messages',
            filter: `chat_id=eq.${chatId}`
          },
          (payload) => {
            console.log('Received updated message:', payload);
            const updatedMessage = payload.new as Message;
            this.notifyNewMessage(updatedMessage);
          }
        );

        this.activeChats.add(chatId);
        console.log(`Added chat ${chatId} to active chats`);
      } else {
        console.log(`Chat ${chatId} is already subscribed`);
      }

      // Set up typing indicators for this chat
      this.setupTypingChannel(chatId);
    } catch (error) {
      console.error(`Error subscribing to chat ${chatId}:`, error);
      this.notifyError(error as Error);
    }
  }

  // Set up typing indicators for a specific chat
  private setupTypingChannel(chatId: string): void {
    try {
      if (!this.currentUserId) {
        console.log('Cannot set up typing channel: currentUserId is not set');
        return;
      }

      const channelName = `typing:${chatId}`;
      console.log(`Setting up typing channel ${channelName}...`);

      this.typingChannel = supabase.channel(channelName);

      this.typingChannel
        .on('broadcast', { event: 'typing' }, (payload) => {
          console.log('Received typing indicator:', payload);
          const { user_id, is_typing } = payload;
          if (user_id !== this.currentUserId) {
            this.notifyTyping(user_id, chatId, is_typing);
          }
        })
        .subscribe((status) => {
          console.log(`Typing channel subscription status for chat ${chatId}:`, status);
          if (status === 'CHANNEL_ERROR') {
            console.error(`Error subscribing to typing channel for chat ${chatId}`);
          }
        });

      console.log(`Typing channel for chat ${chatId} set up`);
    } catch (error) {
      console.error(`Error setting up typing channel for chat ${chatId}:`, error);
      this.notifyError(error as Error);
    }
  }

  // Send typing indicator
  sendTypingIndicator(chatId: string, isTyping: boolean): void {
    try {
      if (!this.currentUserId) {
        console.log('Cannot send typing indicator: currentUserId is not set');
        return;
      }

      if (!this.typingChannel) {
        console.log('Cannot send typing indicator: typingChannel is not set');
        return;
      }

      console.log(`Sending typing indicator for chat ${chatId}: ${isTyping}`);

      this.typingChannel.send({
        type: 'broadcast',
        event: 'typing',
        payload: {
          user_id: this.currentUserId,
          is_typing: isTyping,
        },
      }).then(() => {
        console.log(`Typing indicator sent successfully for chat ${chatId}`);
      }).catch(error => {
        console.error(`Error sending typing indicator for chat ${chatId}:`, error);
        this.notifyError(error);
      });
    } catch (error) {
      console.error(`Error sending typing indicator for chat ${chatId}:`, error);
      this.notifyError(error as Error);
    }
  }

  // Mark message as read
  async markMessageAsRead(messageId: string): Promise<void> {
    try {
      console.log(`Marking message ${messageId} as read`);

      const { data, error } = await supabase
        .from('messages')
        .update({ read: true })
        .eq('id', messageId)
        .select();

      if (error) {
        console.error('Error marking message as read:', error);
        throw error;
      }

      console.log('Message marked as read:', data);
    } catch (error) {
      console.error('Failed to mark message as read:', error);
      this.notifyError(error as Error);
    }
  }

  // Send a new message
  async sendMessage(message: Message): Promise<Message | null> {
    try {
      console.log('Sending message to Supabase:', message);

      const { data, error } = await supabase
        .from('messages')
        .insert([
          {
            id: message.id,
            content: message.content,
            chat_id: message.chat_id,
            user_id: message.sender_id, // Map sender_id to user_id in Supabase
            created_at: message.timestamp || new Date().toISOString(),
            read: false,
          },
        ])
        .select();

      if (error) {
        console.error('Supabase insert error:', error);
        throw error;
      }

      console.log('Message sent successfully:', data);
      return data?.[0] as Message;
    } catch (error) {
      console.error('Error in sendMessage:', error);
      this.notifyError(error as Error);
      return null;
    }
  }

  // Notify handlers of a new message
  private notifyNewMessage(message: Message): void {
    this.messageHandlers.forEach(handler => handler(message));
  }

  // Notify handlers of user status changes
  private notifyUserStatus(userId: string, status: 'online' | 'offline'): void {
    this.userStatusHandlers.forEach(handler => {
      // We need to fetch the user details based on userId
      // For now, we'll create a minimal user object
      const user: User = {
        id: userId,
        name: '', // This would be populated from a user lookup
        email: '',
        status: status
      };
      handler(user, status);
    });
  }

  // Notify handlers of typing indicators
  private notifyTyping(userId: string, chatId: string, isTyping: boolean): void {
    this.typingHandlers.forEach(handler => handler(userId, chatId, isTyping));
  }

  // Notify handlers of errors
  private notifyError(error: Error): void {
    this.errorHandlers.forEach(handler => handler(error));
  }

  // Register handlers
  onMessage(handler: MessageHandler): void {
    this.messageHandlers.push(handler);
  }

  onUserStatus(handler: UserStatusHandler): void {
    this.userStatusHandlers.push(handler);
  }

  onTyping(handler: TypingHandler): void {
    this.typingHandlers.push(handler);
  }

  onError(handler: ErrorHandler): void {
    this.errorHandlers.push(handler);
  }

  // Clean up subscriptions
  cleanup(): void {
    if (this.messageChannel) {
      this.messageChannel.unsubscribe();
      this.messageChannel = null;
    }

    if (this.presenceChannel) {
      this.presenceChannel.unsubscribe();
      this.presenceChannel = null;
    }

    if (this.typingChannel) {
      this.typingChannel.unsubscribe();
      this.typingChannel = null;
    }

    this.activeChats.clear();
  }
}

// Export a singleton instance
export const realtimeService = new RealtimeService();
