// Supabase configuration
// First try to use environment variables, then fall back to hardcoded values if needed
export const SUPABASE_URL = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://edkucvtxscwanfkhqvoi.supabase.co';
export const SUPABASE_ANON_KEY = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImVka3VjdnR4c2N3YW5ma2hxdm9pIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDc4MTAyNjUsImV4cCI6MjA2MzM4NjI2NX0.dSVDE-MP2YAtlII5D0TP6qfZJcf24v_Ex0r9m2mDi9Y';

// Log the configuration being used
console.log('Supabase configuration:');
console.log('- Using URL:', SUPABASE_URL);
console.log('- Using Anon Key:', SUPABASE_ANON_KEY ? 'Set (value hidden)' : 'Not set');
console.log('- Source:', process.env.NEXT_PUBLIC_SUPABASE_URL ? 'Environment variables' : 'Fallback values');
